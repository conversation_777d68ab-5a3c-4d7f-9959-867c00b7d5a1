🎯 关于创作灵感输入
灵感输入的形式：
用户是输入一句话的核心创意？（如"一个程序员穿越到修仙世界"）
W：没错，新手模式用户只输入自己的创意，其他的由ai生成，生成的每一个步骤用户就相当于老板进行审核，审核通过后才会进入下一步。否则用户输入修改建议或者不足ai根据用户输入的修改建议进行修改。
N：
还是需要更详细的描述？（包括主角设定、背景等）
W：由ai生成专业的创作描述或建议，用户可以根据ai的建议进行修改，让ai重新生成，提高创作的质量。
N:
是否支持多种输入方式？（文字描述、关键词标签、问答形式）
W：是的
AI如何处理灵感：
AI是直接基于这个灵感生成完整大纲？
W：你有什么建议吗？
还是先提出问题帮用户完善创意？
W：我觉得你这个想法不错
需要AI提供多个创意发展方向让用户选择吗？
W：我觉得你这个想法不错
📚 关于小说类型和目标群体
类型分类的详细程度：
是简单的大分类（玄幻、都市、历史、科幻等）？
还是需要细分（如玄幻下的修仙、异界、都市修仙等）？
W:细分
是否允许混合类型？
W:我想先知道你的建议
目标群体的定义：
W：关于这里我想听听你专业的建议
男频/女频的划分标准是什么？
是否需要年龄段细分（青少年、成人等）？
W:严禁黄色、色情即可
需要考虑阅读平台的差异吗？（起点、晋江、番茄等）

✍️ 关于章节和字数设置
字数设置的灵活性：
每章字数是固定的还是可以有浮动范围？
W:可以有浮动
是否需要根据小说类型给出推荐字数？
W:可以，用户可以参考ai推荐字数
用户可以中途修改字数设置吗？
W:会影响小说质量吗？如果会影响小说质量就不建议修改了
章节结构的引导：
新手模式是否需要教用户基本的章节结构？（开头、发展、高潮、结尾）
W：是的，可以教授用户学会网文小说创作，而不是一直依赖ai生成，但不强制性。
是否需要提供章节命名的建议？
W:可以让用户自己命名章节，也可以让ai生成章节名，但是确保读者喜欢。
需要AI帮助规划每章的大致内容吗？
W:大致什么内容
🤖 关于AI辅助的深度
W：关于辅助，一句话提高网文小说创作效率。
AI介入的程度：
AI是只提供建议，还是可以直接生成内容？
用户对AI生成内容的控制程度如何？
如何平衡AI辅助和用户创作的主导权？
个性化学习：
AI是否需要学习用户的写作风格？
如何处理用户对AI建议的反馈？
需要记录用户的偏好设置吗？
🎮 关于用户体验
W:这就要你，提供专业化的设计给我看了
引导流程的长度：
整个新手引导需要几个步骤？
每个步骤大概需要用户花多长时间？
用户可以跳过某些步骤吗？
错误处理和返回：
用户填错信息后如何修改？
是否允许用户返回上一步重新选择？
如何保存用户的进度？
💾 关于数据管理
W:我现在的想法是，MCP+数据库。
项目保存和管理：
新手创建的项目如何命名和分类？
W:ai根据用户的创意生成书名，书名就是项目名，用户可以修改。
是否需要项目模板功能？
W:可以，用户可以选择模板，模板可以帮助用户快速上手。
如何处理未完成的引导流程？
W:用户可以保存未完成的项目，下次登录时继续完成。
  