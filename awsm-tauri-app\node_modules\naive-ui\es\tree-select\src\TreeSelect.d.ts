import type { FormValidationStatus } from '../../form/src/public-types';
import type { PopoverProps } from '../../popover';
import type { SelectBaseOption, SelectOption } from '../../select/src/interface';
import type { GetChildren, InternalTreeInst, Key, TreeOption, TreeOverrideNodeClickBehaviorReturn } from '../../tree/src/interface';
import type { OnUpdateValue, TreeSelectNodeProps, TreeSelectOption, TreeSelectRenderLabel, TreeSelectRenderPrefix, TreeSelectRenderSuffix, TreeSelectRenderTag } from './interface';
import { type CheckStrategy } from 'treemate';
import { type HTMLAttributes, type PropType, type SlotsType, type VNode, type VNodeChild } from 'vue';
import { type FollowerInst, type FollowerPlacement } from 'vueuc';
import { type InternalSelectionInst } from '../../_internal';
import { type ExtractPublicPropTypes, type MaybeArray } from '../../_utils';
type OnLoad = (node: TreeSelectOption) => Promise<void>;
export declare const treeSelectProps: {
    readonly renderLabel: PropType<TreeSelectRenderLabel>;
    readonly renderPrefix: PropType<TreeSelectRenderPrefix>;
    readonly renderSuffix: PropType<TreeSelectRenderSuffix>;
    readonly nodeProps: PropType<TreeSelectNodeProps>;
    readonly watchProps: PropType<Array<"defaultCheckedKeys" | "defaultSelectedKeys" | "defaultExpandedKeys">>;
    readonly getChildren: PropType<GetChildren>;
    readonly onBlur: PropType<(e: FocusEvent) => void>;
    readonly onFocus: PropType<(e: FocusEvent) => void>;
    readonly onLoad: PropType<OnLoad>;
    readonly onUpdateShow: PropType<MaybeArray<(show: boolean) => void>>;
    readonly onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    readonly 'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    readonly 'onUpdate:show': PropType<MaybeArray<(show: boolean) => void>>;
    /**
     * @deprecated
     */
    readonly leafOnly: BooleanConstructor;
    readonly allowCheckingNotLoaded: BooleanConstructor;
    readonly filter: PropType<(pattern: string, node: TreeOption) => boolean>;
    readonly defaultExpandAll: BooleanConstructor;
    readonly expandedKeys: PropType<Key[]>;
    readonly keyField: {
        readonly type: StringConstructor;
        readonly default: "key";
    };
    readonly labelField: {
        readonly type: StringConstructor;
        readonly default: "label";
    };
    readonly childrenField: {
        readonly type: StringConstructor;
        readonly default: "children";
    };
    readonly disabledField: {
        readonly type: StringConstructor;
        readonly default: "disabled";
    };
    readonly defaultExpandedKeys: {
        readonly type: PropType<Key[]>;
        readonly default: () => never[];
    };
    readonly indent: {
        readonly type: NumberConstructor;
        readonly default: 24;
    };
    readonly indeterminateKeys: PropType<Key[]>;
    readonly renderSwitcherIcon: PropType<import("../../tree/src/interface").RenderSwitcherIcon>;
    readonly onUpdateIndeterminateKeys: PropType<MaybeArray<import("../../tree/src/Tree").OnUpdateIndeterminateKeys>>;
    readonly 'onUpdate:indeterminateKeys': PropType<MaybeArray<import("../../tree/src/Tree").OnUpdateIndeterminateKeys>>;
    readonly onUpdateExpandedKeys: PropType<MaybeArray<import("../../tree/src/Tree").onUpdateExpandedKeys>>;
    readonly 'onUpdate:expandedKeys': PropType<MaybeArray<import("../../tree/src/Tree").onUpdateExpandedKeys>>;
    readonly overrideDefaultNodeClickBehavior: PropType<import("../../tree").TreeOverrideNodeClickBehavior>;
    readonly bordered: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly cascade: BooleanConstructor;
    readonly checkable: BooleanConstructor;
    readonly clearable: BooleanConstructor;
    readonly clearFilterAfterSelect: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly consistentMenuWidth: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly defaultShow: BooleanConstructor;
    readonly defaultValue: {
        readonly type: PropType<string | number | Array<string | number> | null>;
        readonly default: null;
    };
    readonly disabled: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly filterable: BooleanConstructor;
    readonly checkStrategy: {
        readonly type: PropType<CheckStrategy>;
        readonly default: "all";
    };
    readonly loading: BooleanConstructor;
    readonly maxTagCount: PropType<number | "responsive">;
    readonly multiple: BooleanConstructor;
    readonly showPath: BooleanConstructor;
    readonly separator: {
        readonly type: StringConstructor;
        readonly default: " / ";
    };
    readonly options: {
        readonly type: PropType<TreeSelectOption[]>;
        readonly default: () => never[];
    };
    readonly placeholder: StringConstructor;
    readonly placement: {
        readonly type: PropType<FollowerPlacement>;
        readonly default: "bottom-start";
    };
    readonly show: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly size: PropType<"small" | "medium" | "large">;
    readonly value: PropType<string | number | Array<string | number> | null>;
    readonly to: {
        type: PropType<HTMLElement | string | boolean>;
        default: undefined;
    };
    readonly menuProps: PropType<HTMLAttributes>;
    readonly virtualScroll: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly status: PropType<FormValidationStatus>;
    readonly renderTag: PropType<TreeSelectRenderTag>;
    readonly ellipsisTagPopoverProps: PropType<PopoverProps>;
    readonly theme: PropType<import("../../_mixins").Theme<"TreeSelect", {
        menuPadding: string;
        menuColor: string;
        menuBoxShadow: string;
        menuBorderRadius: string;
        menuHeight: string;
        actionDividerColor: string;
        actionTextColor: string;
        actionPadding: string;
        headerDividerColor: string;
        headerTextColor: string;
        headerPadding: string;
    }, {
        Tree: import("../../_mixins").Theme<"Tree", {
            fontSize: string;
            lineHeight: string;
            nodeHeight: string;
            nodeWrapperPadding: string;
            nodeBorderRadius: string;
            nodeColorHover: string;
            nodeColorPressed: string;
            nodeColorActive: string;
            arrowColor: string;
            nodeTextColor: string;
            nodeTextColorDisabled: string;
            loadingColor: string;
            dropMarkColor: string;
            lineColor: string;
        }, {
            Checkbox: import("../../_mixins").Theme<"Checkbox", {
                labelLineHeight: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                borderRadius: string;
                color: string;
                colorChecked: string;
                colorDisabled: string;
                colorDisabledChecked: string;
                colorTableHeader: string;
                colorTableHeaderModal: string;
                colorTableHeaderPopover: string;
                checkMarkColor: string;
                checkMarkColorDisabled: string;
                checkMarkColorDisabledChecked: string;
                border: string;
                borderDisabled: string;
                borderDisabledChecked: string;
                borderChecked: string;
                borderFocus: string;
                boxShadowFocus: string;
                textColor: string;
                textColorDisabled: string;
                sizeSmall: string;
                sizeMedium: string;
                sizeLarge: string;
                labelPadding: string;
                labelFontWeight: string;
            }, any>;
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"TreeSelect", {
        menuPadding: string;
        menuColor: string;
        menuBoxShadow: string;
        menuBorderRadius: string;
        menuHeight: string;
        actionDividerColor: string;
        actionTextColor: string;
        actionPadding: string;
        headerDividerColor: string;
        headerTextColor: string;
        headerPadding: string;
    }, {
        Tree: import("../../_mixins").Theme<"Tree", {
            fontSize: string;
            lineHeight: string;
            nodeHeight: string;
            nodeWrapperPadding: string;
            nodeBorderRadius: string;
            nodeColorHover: string;
            nodeColorPressed: string;
            nodeColorActive: string;
            arrowColor: string;
            nodeTextColor: string;
            nodeTextColorDisabled: string;
            loadingColor: string;
            dropMarkColor: string;
            lineColor: string;
        }, {
            Checkbox: import("../../_mixins").Theme<"Checkbox", {
                labelLineHeight: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                borderRadius: string;
                color: string;
                colorChecked: string;
                colorDisabled: string;
                colorDisabledChecked: string;
                colorTableHeader: string;
                colorTableHeaderModal: string;
                colorTableHeaderPopover: string;
                checkMarkColor: string;
                checkMarkColorDisabled: string;
                checkMarkColorDisabledChecked: string;
                border: string;
                borderDisabled: string;
                borderDisabledChecked: string;
                borderChecked: string;
                borderFocus: string;
                boxShadowFocus: string;
                textColor: string;
                textColorDisabled: string;
                sizeSmall: string;
                sizeMedium: string;
                sizeLarge: string;
                labelPadding: string;
                labelFontWeight: string;
            }, any>;
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"TreeSelect", {
        menuPadding: string;
        menuColor: string;
        menuBoxShadow: string;
        menuBorderRadius: string;
        menuHeight: string;
        actionDividerColor: string;
        actionTextColor: string;
        actionPadding: string;
        headerDividerColor: string;
        headerTextColor: string;
        headerPadding: string;
    }, {
        Tree: import("../../_mixins").Theme<"Tree", {
            fontSize: string;
            lineHeight: string;
            nodeHeight: string;
            nodeWrapperPadding: string;
            nodeBorderRadius: string;
            nodeColorHover: string;
            nodeColorPressed: string;
            nodeColorActive: string;
            arrowColor: string;
            nodeTextColor: string;
            nodeTextColorDisabled: string;
            loadingColor: string;
            dropMarkColor: string;
            lineColor: string;
        }, {
            Checkbox: import("../../_mixins").Theme<"Checkbox", {
                labelLineHeight: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                borderRadius: string;
                color: string;
                colorChecked: string;
                colorDisabled: string;
                colorDisabledChecked: string;
                colorTableHeader: string;
                colorTableHeaderModal: string;
                colorTableHeaderPopover: string;
                checkMarkColor: string;
                checkMarkColorDisabled: string;
                checkMarkColorDisabledChecked: string;
                border: string;
                borderDisabled: string;
                borderDisabledChecked: string;
                borderChecked: string;
                borderFocus: string;
                boxShadowFocus: string;
                textColor: string;
                textColorDisabled: string;
                sizeSmall: string;
                sizeMedium: string;
                sizeLarge: string;
                labelPadding: string;
                labelFontWeight: string;
            }, any>;
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>>;
};
export type TreeSelectProps = ExtractPublicPropTypes<typeof treeSelectProps>;
export interface TreeSelectSlots {
    header?: () => VNode[];
    action?: () => VNode[];
    arrow?: () => VNode[];
    empty?: () => VNode[];
}
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    readonly renderLabel: PropType<TreeSelectRenderLabel>;
    readonly renderPrefix: PropType<TreeSelectRenderPrefix>;
    readonly renderSuffix: PropType<TreeSelectRenderSuffix>;
    readonly nodeProps: PropType<TreeSelectNodeProps>;
    readonly watchProps: PropType<Array<"defaultCheckedKeys" | "defaultSelectedKeys" | "defaultExpandedKeys">>;
    readonly getChildren: PropType<GetChildren>;
    readonly onBlur: PropType<(e: FocusEvent) => void>;
    readonly onFocus: PropType<(e: FocusEvent) => void>;
    readonly onLoad: PropType<OnLoad>;
    readonly onUpdateShow: PropType<MaybeArray<(show: boolean) => void>>;
    readonly onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    readonly 'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    readonly 'onUpdate:show': PropType<MaybeArray<(show: boolean) => void>>;
    /**
     * @deprecated
     */
    readonly leafOnly: BooleanConstructor;
    readonly allowCheckingNotLoaded: BooleanConstructor;
    readonly filter: PropType<(pattern: string, node: TreeOption) => boolean>;
    readonly defaultExpandAll: BooleanConstructor;
    readonly expandedKeys: PropType<Key[]>;
    readonly keyField: {
        readonly type: StringConstructor;
        readonly default: "key";
    };
    readonly labelField: {
        readonly type: StringConstructor;
        readonly default: "label";
    };
    readonly childrenField: {
        readonly type: StringConstructor;
        readonly default: "children";
    };
    readonly disabledField: {
        readonly type: StringConstructor;
        readonly default: "disabled";
    };
    readonly defaultExpandedKeys: {
        readonly type: PropType<Key[]>;
        readonly default: () => never[];
    };
    readonly indent: {
        readonly type: NumberConstructor;
        readonly default: 24;
    };
    readonly indeterminateKeys: PropType<Key[]>;
    readonly renderSwitcherIcon: PropType<import("../../tree/src/interface").RenderSwitcherIcon>;
    readonly onUpdateIndeterminateKeys: PropType<MaybeArray<import("../../tree/src/Tree").OnUpdateIndeterminateKeys>>;
    readonly 'onUpdate:indeterminateKeys': PropType<MaybeArray<import("../../tree/src/Tree").OnUpdateIndeterminateKeys>>;
    readonly onUpdateExpandedKeys: PropType<MaybeArray<import("../../tree/src/Tree").onUpdateExpandedKeys>>;
    readonly 'onUpdate:expandedKeys': PropType<MaybeArray<import("../../tree/src/Tree").onUpdateExpandedKeys>>;
    readonly overrideDefaultNodeClickBehavior: PropType<import("../../tree").TreeOverrideNodeClickBehavior>;
    readonly bordered: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly cascade: BooleanConstructor;
    readonly checkable: BooleanConstructor;
    readonly clearable: BooleanConstructor;
    readonly clearFilterAfterSelect: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly consistentMenuWidth: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly defaultShow: BooleanConstructor;
    readonly defaultValue: {
        readonly type: PropType<string | number | Array<string | number> | null>;
        readonly default: null;
    };
    readonly disabled: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly filterable: BooleanConstructor;
    readonly checkStrategy: {
        readonly type: PropType<CheckStrategy>;
        readonly default: "all";
    };
    readonly loading: BooleanConstructor;
    readonly maxTagCount: PropType<number | "responsive">;
    readonly multiple: BooleanConstructor;
    readonly showPath: BooleanConstructor;
    readonly separator: {
        readonly type: StringConstructor;
        readonly default: " / ";
    };
    readonly options: {
        readonly type: PropType<TreeSelectOption[]>;
        readonly default: () => never[];
    };
    readonly placeholder: StringConstructor;
    readonly placement: {
        readonly type: PropType<FollowerPlacement>;
        readonly default: "bottom-start";
    };
    readonly show: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly size: PropType<"small" | "medium" | "large">;
    readonly value: PropType<string | number | Array<string | number> | null>;
    readonly to: {
        type: PropType<HTMLElement | string | boolean>;
        default: undefined;
    };
    readonly menuProps: PropType<HTMLAttributes>;
    readonly virtualScroll: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly status: PropType<FormValidationStatus>;
    readonly renderTag: PropType<TreeSelectRenderTag>;
    readonly ellipsisTagPopoverProps: PropType<PopoverProps>;
    readonly theme: PropType<import("../../_mixins").Theme<"TreeSelect", {
        menuPadding: string;
        menuColor: string;
        menuBoxShadow: string;
        menuBorderRadius: string;
        menuHeight: string;
        actionDividerColor: string;
        actionTextColor: string;
        actionPadding: string;
        headerDividerColor: string;
        headerTextColor: string;
        headerPadding: string;
    }, {
        Tree: import("../../_mixins").Theme<"Tree", {
            fontSize: string;
            lineHeight: string;
            nodeHeight: string;
            nodeWrapperPadding: string;
            nodeBorderRadius: string;
            nodeColorHover: string;
            nodeColorPressed: string;
            nodeColorActive: string;
            arrowColor: string;
            nodeTextColor: string;
            nodeTextColorDisabled: string;
            loadingColor: string;
            dropMarkColor: string;
            lineColor: string;
        }, {
            Checkbox: import("../../_mixins").Theme<"Checkbox", {
                labelLineHeight: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                borderRadius: string;
                color: string;
                colorChecked: string;
                colorDisabled: string;
                colorDisabledChecked: string;
                colorTableHeader: string;
                colorTableHeaderModal: string;
                colorTableHeaderPopover: string;
                checkMarkColor: string;
                checkMarkColorDisabled: string;
                checkMarkColorDisabledChecked: string;
                border: string;
                borderDisabled: string;
                borderDisabledChecked: string;
                borderChecked: string;
                borderFocus: string;
                boxShadowFocus: string;
                textColor: string;
                textColorDisabled: string;
                sizeSmall: string;
                sizeMedium: string;
                sizeLarge: string;
                labelPadding: string;
                labelFontWeight: string;
            }, any>;
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"TreeSelect", {
        menuPadding: string;
        menuColor: string;
        menuBoxShadow: string;
        menuBorderRadius: string;
        menuHeight: string;
        actionDividerColor: string;
        actionTextColor: string;
        actionPadding: string;
        headerDividerColor: string;
        headerTextColor: string;
        headerPadding: string;
    }, {
        Tree: import("../../_mixins").Theme<"Tree", {
            fontSize: string;
            lineHeight: string;
            nodeHeight: string;
            nodeWrapperPadding: string;
            nodeBorderRadius: string;
            nodeColorHover: string;
            nodeColorPressed: string;
            nodeColorActive: string;
            arrowColor: string;
            nodeTextColor: string;
            nodeTextColorDisabled: string;
            loadingColor: string;
            dropMarkColor: string;
            lineColor: string;
        }, {
            Checkbox: import("../../_mixins").Theme<"Checkbox", {
                labelLineHeight: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                borderRadius: string;
                color: string;
                colorChecked: string;
                colorDisabled: string;
                colorDisabledChecked: string;
                colorTableHeader: string;
                colorTableHeaderModal: string;
                colorTableHeaderPopover: string;
                checkMarkColor: string;
                checkMarkColorDisabled: string;
                checkMarkColorDisabledChecked: string;
                border: string;
                borderDisabled: string;
                borderDisabledChecked: string;
                borderChecked: string;
                borderFocus: string;
                boxShadowFocus: string;
                textColor: string;
                textColorDisabled: string;
                sizeSmall: string;
                sizeMedium: string;
                sizeLarge: string;
                labelPadding: string;
                labelFontWeight: string;
            }, any>;
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"TreeSelect", {
        menuPadding: string;
        menuColor: string;
        menuBoxShadow: string;
        menuBorderRadius: string;
        menuHeight: string;
        actionDividerColor: string;
        actionTextColor: string;
        actionPadding: string;
        headerDividerColor: string;
        headerTextColor: string;
        headerPadding: string;
    }, {
        Tree: import("../../_mixins").Theme<"Tree", {
            fontSize: string;
            lineHeight: string;
            nodeHeight: string;
            nodeWrapperPadding: string;
            nodeBorderRadius: string;
            nodeColorHover: string;
            nodeColorPressed: string;
            nodeColorActive: string;
            arrowColor: string;
            nodeTextColor: string;
            nodeTextColorDisabled: string;
            loadingColor: string;
            dropMarkColor: string;
            lineColor: string;
        }, {
            Checkbox: import("../../_mixins").Theme<"Checkbox", {
                labelLineHeight: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                borderRadius: string;
                color: string;
                colorChecked: string;
                colorDisabled: string;
                colorDisabledChecked: string;
                colorTableHeader: string;
                colorTableHeaderModal: string;
                colorTableHeaderPopover: string;
                checkMarkColor: string;
                checkMarkColorDisabled: string;
                checkMarkColorDisabledChecked: string;
                border: string;
                borderDisabled: string;
                borderDisabledChecked: string;
                borderChecked: string;
                borderFocus: string;
                boxShadowFocus: string;
                textColor: string;
                textColorDisabled: string;
                sizeSmall: string;
                sizeMedium: string;
                sizeLarge: string;
                labelPadding: string;
                labelFontWeight: string;
            }, any>;
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>>;
}>, {
    menuElRef: import("vue").Ref<HTMLDivElement | null, HTMLDivElement | null>;
    mergedStatus: import("vue").ComputedRef<FormValidationStatus | undefined>;
    triggerInstRef: import("vue").Ref<{
        isComposing: boolean;
        focus: () => void;
        focusInput: () => void;
        blur: () => void;
        blurInput: () => void;
        $el: HTMLElement;
    } | null, InternalSelectionInst | {
        isComposing: boolean;
        focus: () => void;
        focusInput: () => void;
        blur: () => void;
        blurInput: () => void;
        $el: HTMLElement;
    } | null>;
    followerInstRef: import("vue").Ref<{
        syncPosition: () => void;
    } | null, FollowerInst | {
        syncPosition: () => void;
    } | null>;
    treeInstRef: import("vue").Ref<{
        handleKeydown: (e: KeyboardEvent) => {
            enterBehavior: TreeOverrideNodeClickBehaviorReturn | null;
        };
    } | null, InternalTreeInst | {
        handleKeydown: (e: KeyboardEvent) => {
            enterBehavior: TreeOverrideNodeClickBehaviorReturn | null;
        };
    } | null>;
    mergedClsPrefix: import("vue").Ref<string, string>;
    mergedValue: import("vue").ComputedRef<string | number | (string | number)[] | null>;
    mergedShow: import("vue").ComputedRef<boolean>;
    namespace: import("vue").ComputedRef<string | undefined>;
    adjustedTo: import("vue").ComputedRef<string | HTMLElement>;
    isMounted: Readonly<import("vue").Ref<boolean, boolean>>;
    focused: import("vue").Ref<boolean, boolean>;
    menuPadding: import("vue").ComputedRef<string>;
    mergedPlaceholder: import("vue").ComputedRef<string>;
    mergedExpandedKeys: import("vue").ComputedRef<Key[] | undefined>;
    treeSelectedKeys: import("vue").ComputedRef<Key[]>;
    treeCheckedKeys: import("vue").ComputedRef<Key[]>;
    mergedSize: import("vue").ComputedRef<"small" | "medium" | "large">;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    selectedOption: import("vue").ComputedRef<SelectBaseOption<string | number, string | ((option: SelectBaseOption<string | number, string | any>, selected: boolean) => VNodeChild)> | null>;
    selectedOptions: import("vue").ComputedRef<SelectBaseOption<string | number, string | ((option: SelectBaseOption<string | number, string | any>, selected: boolean) => VNodeChild)>[] | null>;
    pattern: import("vue").Ref<string, string>;
    pendingNodeKey: import("vue").Ref<string | number | null, string | number | null>;
    mergedCascade: import("vue").ComputedRef<boolean>;
    mergedFilter: import("vue").ComputedRef<(pattern: string, node: TreeOption) => boolean>;
    selectionRenderTag: import("vue").ComputedRef<(({ option, handleClose }: {
        option: SelectOption;
        handleClose: () => void;
    }) => VNodeChild) | undefined>;
    handleTriggerOrMenuResize: () => void;
    doUpdateExpandedKeys: (keys: Key[], option: Array<TreeSelectOption | null>, meta: {
        node: TreeSelectOption;
        action: "expand" | "collapse";
    } | {
        node: null;
        action: "filter";
    }) => void;
    handleMenuLeave: () => void;
    handleTriggerClick: () => void;
    handleMenuClickoutside: (e: MouseEvent) => void;
    handleUpdateCheckedKeys: (keys: Key[], _: unknown, meta: {
        node: TreeOption | null;
        action: "check" | "uncheck";
    }) => void;
    handleUpdateIndeterminateKeys: (keys: Key[]) => void;
    handleTriggerFocus: (e: FocusEvent) => void;
    handleTriggerBlur: (e: FocusEvent) => void;
    handleMenuFocusin: (e: FocusEvent) => void;
    handleMenuFocusout: (e: FocusEvent) => void;
    handleClear: (e: MouseEvent) => void;
    handleDeleteOption: (option: SelectBaseOption) => void;
    handlePatternInput: (e: InputEvent) => void;
    handleKeydown: (e: KeyboardEvent) => void;
    handleTabOut: () => void;
    handleMenuMousedown: (e: MouseEvent) => void;
    mergedTheme: import("vue").ComputedRef<{
        common: import("../..").ThemeCommonVars;
        self: {
            menuPadding: string;
            menuColor: string;
            menuBoxShadow: string;
            menuBorderRadius: string;
            menuHeight: string;
            actionDividerColor: string;
            actionTextColor: string;
            actionPadding: string;
            headerDividerColor: string;
            headerTextColor: string;
            headerPadding: string;
        };
        peers: {
            Tree: import("../../_mixins").Theme<"Tree", {
                fontSize: string;
                lineHeight: string;
                nodeHeight: string;
                nodeWrapperPadding: string;
                nodeBorderRadius: string;
                nodeColorHover: string;
                nodeColorPressed: string;
                nodeColorActive: string;
                arrowColor: string;
                nodeTextColor: string;
                nodeTextColorDisabled: string;
                loadingColor: string;
                dropMarkColor: string;
                lineColor: string;
            }, {
                Checkbox: import("../../_mixins").Theme<"Checkbox", {
                    labelLineHeight: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    borderRadius: string;
                    color: string;
                    colorChecked: string;
                    colorDisabled: string;
                    colorDisabledChecked: string;
                    colorTableHeader: string;
                    colorTableHeaderModal: string;
                    colorTableHeaderPopover: string;
                    checkMarkColor: string;
                    checkMarkColorDisabled: string;
                    checkMarkColorDisabledChecked: string;
                    border: string;
                    borderDisabled: string;
                    borderDisabledChecked: string;
                    borderChecked: string;
                    borderFocus: string;
                    boxShadowFocus: string;
                    textColor: string;
                    textColorDisabled: string;
                    sizeSmall: string;
                    sizeMedium: string;
                    sizeLarge: string;
                    labelPadding: string;
                    labelFontWeight: string;
                }, any>;
                Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                    height: string;
                    width: string;
                    borderRadius: string;
                    color: string;
                    colorHover: string;
                    railInsetHorizontalBottom: string;
                    railInsetHorizontalTop: string;
                    railInsetVerticalRight: string;
                    railInsetVerticalLeft: string;
                    railColor: string;
                }, any>;
                Empty: import("../../_mixins").Theme<"Empty", {
                    fontSizeTiny: string;
                    fontSizeSmall: string;
                    fontSizeMedium: string;
                    fontSizeLarge: string;
                    fontSizeHuge: string;
                    textColor: string;
                    iconColor: string;
                    extraTextColor: string;
                    iconSizeTiny: string;
                    iconSizeSmall: string;
                    iconSizeMedium: string;
                    iconSizeLarge: string;
                    iconSizeHuge: string;
                }, any>;
            }>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
            InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                borderRadius: string;
                fontWeight: string;
                textColor: string;
                textColorDisabled: string;
                placeholderColor: string;
                placeholderColorDisabled: string;
                color: string;
                colorDisabled: string;
                colorActive: string;
                border: string;
                borderHover: string;
                borderActive: string;
                borderFocus: string;
                boxShadowHover: string;
                boxShadowActive: string;
                boxShadowFocus: string;
                caretColor: string;
                arrowColor: string;
                arrowColorDisabled: string;
                loadingColor: string;
                borderWarning: string;
                borderHoverWarning: string;
                borderActiveWarning: string;
                borderFocusWarning: string;
                boxShadowHoverWarning: string;
                boxShadowActiveWarning: string;
                boxShadowFocusWarning: string;
                colorActiveWarning: string;
                caretColorWarning: string;
                borderError: string;
                borderHoverError: string;
                borderActiveError: string;
                borderFocusError: string;
                boxShadowHoverError: string;
                boxShadowActiveError: string;
                boxShadowFocusError: string;
                colorActiveError: string;
                caretColorError: string;
                clearColor: string;
                clearColorHover: string;
                clearColorPressed: string;
                paddingSingle: string;
                paddingMultiple: string;
                clearSize: string;
                arrowSize: string;
            }, {
                Popover: import("../../_mixins").Theme<"Popover", {
                    fontSize: string;
                    borderRadius: string;
                    color: string;
                    dividerColor: string;
                    textColor: string;
                    boxShadow: string;
                    space: string;
                    spaceArrow: string;
                    arrowOffset: string;
                    arrowOffsetVertical: string;
                    arrowHeight: string;
                    padding: string;
                }, any>;
            }>;
        };
        peerOverrides: {
            Tree?: {
                peers?: {
                    Checkbox?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Checkbox", {
                        labelLineHeight: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        borderRadius: string;
                        color: string;
                        colorChecked: string;
                        colorDisabled: string;
                        colorDisabledChecked: string;
                        colorTableHeader: string;
                        colorTableHeaderModal: string;
                        colorTableHeaderPopover: string;
                        checkMarkColor: string;
                        checkMarkColorDisabled: string;
                        checkMarkColorDisabledChecked: string;
                        border: string;
                        borderDisabled: string;
                        borderDisabledChecked: string;
                        borderChecked: string;
                        borderFocus: string;
                        boxShadowFocus: string;
                        textColor: string;
                        textColorDisabled: string;
                        sizeSmall: string;
                        sizeMedium: string;
                        sizeLarge: string;
                        labelPadding: string;
                        labelFontWeight: string;
                    }, any>> | undefined;
                    Scrollbar?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Scrollbar", {
                        height: string;
                        width: string;
                        borderRadius: string;
                        color: string;
                        colorHover: string;
                        railInsetHorizontalBottom: string;
                        railInsetHorizontalTop: string;
                        railInsetVerticalRight: string;
                        railInsetVerticalLeft: string;
                        railColor: string;
                    }, any>> | undefined;
                    Empty?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Empty", {
                        fontSizeTiny: string;
                        fontSizeSmall: string;
                        fontSizeMedium: string;
                        fontSizeLarge: string;
                        fontSizeHuge: string;
                        textColor: string;
                        iconColor: string;
                        extraTextColor: string;
                        iconSizeTiny: string;
                        iconSizeSmall: string;
                        iconSizeMedium: string;
                        iconSizeLarge: string;
                        iconSizeHuge: string;
                    }, any>> | undefined;
                } | undefined;
            } | undefined;
            Empty?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
            InternalSelection?: {
                peers?: {
                    Popover?: import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Popover", {
                        fontSize: string;
                        borderRadius: string;
                        color: string;
                        dividerColor: string;
                        textColor: string;
                        boxShadow: string;
                        space: string;
                        spaceArrow: string;
                        arrowOffset: string;
                        arrowOffsetVertical: string;
                        arrowHeight: string;
                        padding: string;
                    }, any>> | undefined;
                } | undefined;
            } | undefined;
        };
    }>;
    cssVars: import("vue").ComputedRef<{
        '--n-menu-box-shadow': string;
        '--n-menu-border-radius': string;
        '--n-menu-color': string;
        '--n-menu-height': string;
        '--n-bezier': string;
        '--n-action-padding': string;
        '--n-action-text-color': string;
        '--n-action-divider-color': string;
        '--n-header-padding': string;
        '--n-header-text-color': string;
        '--n-header-divider-color': string;
    }> | undefined;
    themeClass: import("vue").Ref<string, string> | undefined;
    onRender: (() => void) | undefined;
    getCheckedData: () => {
        keys: Array<string | number>;
        options: Array<TreeSelectOption | null>;
    };
    getIndeterminateData: () => {
        keys: Array<string | number>;
        options: Array<TreeSelectOption | null>;
    };
    focus: () => void;
    focusInput: () => void;
    blur: () => void;
    blurInput: () => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    readonly renderLabel: PropType<TreeSelectRenderLabel>;
    readonly renderPrefix: PropType<TreeSelectRenderPrefix>;
    readonly renderSuffix: PropType<TreeSelectRenderSuffix>;
    readonly nodeProps: PropType<TreeSelectNodeProps>;
    readonly watchProps: PropType<Array<"defaultCheckedKeys" | "defaultSelectedKeys" | "defaultExpandedKeys">>;
    readonly getChildren: PropType<GetChildren>;
    readonly onBlur: PropType<(e: FocusEvent) => void>;
    readonly onFocus: PropType<(e: FocusEvent) => void>;
    readonly onLoad: PropType<OnLoad>;
    readonly onUpdateShow: PropType<MaybeArray<(show: boolean) => void>>;
    readonly onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    readonly 'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    readonly 'onUpdate:show': PropType<MaybeArray<(show: boolean) => void>>;
    /**
     * @deprecated
     */
    readonly leafOnly: BooleanConstructor;
    readonly allowCheckingNotLoaded: BooleanConstructor;
    readonly filter: PropType<(pattern: string, node: TreeOption) => boolean>;
    readonly defaultExpandAll: BooleanConstructor;
    readonly expandedKeys: PropType<Key[]>;
    readonly keyField: {
        readonly type: StringConstructor;
        readonly default: "key";
    };
    readonly labelField: {
        readonly type: StringConstructor;
        readonly default: "label";
    };
    readonly childrenField: {
        readonly type: StringConstructor;
        readonly default: "children";
    };
    readonly disabledField: {
        readonly type: StringConstructor;
        readonly default: "disabled";
    };
    readonly defaultExpandedKeys: {
        readonly type: PropType<Key[]>;
        readonly default: () => never[];
    };
    readonly indent: {
        readonly type: NumberConstructor;
        readonly default: 24;
    };
    readonly indeterminateKeys: PropType<Key[]>;
    readonly renderSwitcherIcon: PropType<import("../../tree/src/interface").RenderSwitcherIcon>;
    readonly onUpdateIndeterminateKeys: PropType<MaybeArray<import("../../tree/src/Tree").OnUpdateIndeterminateKeys>>;
    readonly 'onUpdate:indeterminateKeys': PropType<MaybeArray<import("../../tree/src/Tree").OnUpdateIndeterminateKeys>>;
    readonly onUpdateExpandedKeys: PropType<MaybeArray<import("../../tree/src/Tree").onUpdateExpandedKeys>>;
    readonly 'onUpdate:expandedKeys': PropType<MaybeArray<import("../../tree/src/Tree").onUpdateExpandedKeys>>;
    readonly overrideDefaultNodeClickBehavior: PropType<import("../../tree").TreeOverrideNodeClickBehavior>;
    readonly bordered: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly cascade: BooleanConstructor;
    readonly checkable: BooleanConstructor;
    readonly clearable: BooleanConstructor;
    readonly clearFilterAfterSelect: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly consistentMenuWidth: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly defaultShow: BooleanConstructor;
    readonly defaultValue: {
        readonly type: PropType<string | number | Array<string | number> | null>;
        readonly default: null;
    };
    readonly disabled: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly filterable: BooleanConstructor;
    readonly checkStrategy: {
        readonly type: PropType<CheckStrategy>;
        readonly default: "all";
    };
    readonly loading: BooleanConstructor;
    readonly maxTagCount: PropType<number | "responsive">;
    readonly multiple: BooleanConstructor;
    readonly showPath: BooleanConstructor;
    readonly separator: {
        readonly type: StringConstructor;
        readonly default: " / ";
    };
    readonly options: {
        readonly type: PropType<TreeSelectOption[]>;
        readonly default: () => never[];
    };
    readonly placeholder: StringConstructor;
    readonly placement: {
        readonly type: PropType<FollowerPlacement>;
        readonly default: "bottom-start";
    };
    readonly show: {
        readonly type: PropType<boolean | undefined>;
        readonly default: undefined;
    };
    readonly size: PropType<"small" | "medium" | "large">;
    readonly value: PropType<string | number | Array<string | number> | null>;
    readonly to: {
        type: PropType<HTMLElement | string | boolean>;
        default: undefined;
    };
    readonly menuProps: PropType<HTMLAttributes>;
    readonly virtualScroll: {
        readonly type: BooleanConstructor;
        readonly default: true;
    };
    readonly status: PropType<FormValidationStatus>;
    readonly renderTag: PropType<TreeSelectRenderTag>;
    readonly ellipsisTagPopoverProps: PropType<PopoverProps>;
    readonly theme: PropType<import("../../_mixins").Theme<"TreeSelect", {
        menuPadding: string;
        menuColor: string;
        menuBoxShadow: string;
        menuBorderRadius: string;
        menuHeight: string;
        actionDividerColor: string;
        actionTextColor: string;
        actionPadding: string;
        headerDividerColor: string;
        headerTextColor: string;
        headerPadding: string;
    }, {
        Tree: import("../../_mixins").Theme<"Tree", {
            fontSize: string;
            lineHeight: string;
            nodeHeight: string;
            nodeWrapperPadding: string;
            nodeBorderRadius: string;
            nodeColorHover: string;
            nodeColorPressed: string;
            nodeColorActive: string;
            arrowColor: string;
            nodeTextColor: string;
            nodeTextColorDisabled: string;
            loadingColor: string;
            dropMarkColor: string;
            lineColor: string;
        }, {
            Checkbox: import("../../_mixins").Theme<"Checkbox", {
                labelLineHeight: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                borderRadius: string;
                color: string;
                colorChecked: string;
                colorDisabled: string;
                colorDisabledChecked: string;
                colorTableHeader: string;
                colorTableHeaderModal: string;
                colorTableHeaderPopover: string;
                checkMarkColor: string;
                checkMarkColorDisabled: string;
                checkMarkColorDisabledChecked: string;
                border: string;
                borderDisabled: string;
                borderDisabledChecked: string;
                borderChecked: string;
                borderFocus: string;
                boxShadowFocus: string;
                textColor: string;
                textColorDisabled: string;
                sizeSmall: string;
                sizeMedium: string;
                sizeLarge: string;
                labelPadding: string;
                labelFontWeight: string;
            }, any>;
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"TreeSelect", {
        menuPadding: string;
        menuColor: string;
        menuBoxShadow: string;
        menuBorderRadius: string;
        menuHeight: string;
        actionDividerColor: string;
        actionTextColor: string;
        actionPadding: string;
        headerDividerColor: string;
        headerTextColor: string;
        headerPadding: string;
    }, {
        Tree: import("../../_mixins").Theme<"Tree", {
            fontSize: string;
            lineHeight: string;
            nodeHeight: string;
            nodeWrapperPadding: string;
            nodeBorderRadius: string;
            nodeColorHover: string;
            nodeColorPressed: string;
            nodeColorActive: string;
            arrowColor: string;
            nodeTextColor: string;
            nodeTextColorDisabled: string;
            loadingColor: string;
            dropMarkColor: string;
            lineColor: string;
        }, {
            Checkbox: import("../../_mixins").Theme<"Checkbox", {
                labelLineHeight: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                borderRadius: string;
                color: string;
                colorChecked: string;
                colorDisabled: string;
                colorDisabledChecked: string;
                colorTableHeader: string;
                colorTableHeaderModal: string;
                colorTableHeaderPopover: string;
                checkMarkColor: string;
                checkMarkColorDisabled: string;
                checkMarkColorDisabledChecked: string;
                border: string;
                borderDisabled: string;
                borderDisabledChecked: string;
                borderChecked: string;
                borderFocus: string;
                boxShadowFocus: string;
                textColor: string;
                textColorDisabled: string;
                sizeSmall: string;
                sizeMedium: string;
                sizeLarge: string;
                labelPadding: string;
                labelFontWeight: string;
            }, any>;
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"TreeSelect", {
        menuPadding: string;
        menuColor: string;
        menuBoxShadow: string;
        menuBorderRadius: string;
        menuHeight: string;
        actionDividerColor: string;
        actionTextColor: string;
        actionPadding: string;
        headerDividerColor: string;
        headerTextColor: string;
        headerPadding: string;
    }, {
        Tree: import("../../_mixins").Theme<"Tree", {
            fontSize: string;
            lineHeight: string;
            nodeHeight: string;
            nodeWrapperPadding: string;
            nodeBorderRadius: string;
            nodeColorHover: string;
            nodeColorPressed: string;
            nodeColorActive: string;
            arrowColor: string;
            nodeTextColor: string;
            nodeTextColorDisabled: string;
            loadingColor: string;
            dropMarkColor: string;
            lineColor: string;
        }, {
            Checkbox: import("../../_mixins").Theme<"Checkbox", {
                labelLineHeight: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                borderRadius: string;
                color: string;
                colorChecked: string;
                colorDisabled: string;
                colorDisabledChecked: string;
                colorTableHeader: string;
                colorTableHeaderModal: string;
                colorTableHeaderPopover: string;
                checkMarkColor: string;
                checkMarkColorDisabled: string;
                checkMarkColorDisabledChecked: string;
                border: string;
                borderDisabled: string;
                borderDisabledChecked: string;
                borderChecked: string;
                borderFocus: string;
                boxShadowFocus: string;
                textColor: string;
                textColorDisabled: string;
                sizeSmall: string;
                sizeMedium: string;
                sizeLarge: string;
                labelPadding: string;
                labelFontWeight: string;
            }, any>;
            Scrollbar: import("../../_mixins").Theme<"Scrollbar", {
                height: string;
                width: string;
                borderRadius: string;
                color: string;
                colorHover: string;
                railInsetHorizontalBottom: string;
                railInsetHorizontalTop: string;
                railInsetVerticalRight: string;
                railInsetVerticalLeft: string;
                railColor: string;
            }, any>;
            Empty: import("../../_mixins").Theme<"Empty", {
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                fontSizeHuge: string;
                textColor: string;
                iconColor: string;
                extraTextColor: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                iconSizeHuge: string;
            }, any>;
        }>;
        Empty: import("../../_mixins").Theme<"Empty", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            fontSizeHuge: string;
            textColor: string;
            iconColor: string;
            extraTextColor: string;
            iconSizeTiny: string;
            iconSizeSmall: string;
            iconSizeMedium: string;
            iconSizeLarge: string;
            iconSizeHuge: string;
        }, any>;
        InternalSelection: import("../../_mixins").Theme<"InternalSelection", {
            fontSizeTiny: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            heightTiny: string;
            heightSmall: string;
            heightMedium: string;
            heightLarge: string;
            borderRadius: string;
            fontWeight: string;
            textColor: string;
            textColorDisabled: string;
            placeholderColor: string;
            placeholderColorDisabled: string;
            color: string;
            colorDisabled: string;
            colorActive: string;
            border: string;
            borderHover: string;
            borderActive: string;
            borderFocus: string;
            boxShadowHover: string;
            boxShadowActive: string;
            boxShadowFocus: string;
            caretColor: string;
            arrowColor: string;
            arrowColorDisabled: string;
            loadingColor: string;
            borderWarning: string;
            borderHoverWarning: string;
            borderActiveWarning: string;
            borderFocusWarning: string;
            boxShadowHoverWarning: string;
            boxShadowActiveWarning: string;
            boxShadowFocusWarning: string;
            colorActiveWarning: string;
            caretColorWarning: string;
            borderError: string;
            borderHoverError: string;
            borderActiveError: string;
            borderFocusError: string;
            boxShadowHoverError: string;
            boxShadowActiveError: string;
            boxShadowFocusError: string;
            colorActiveError: string;
            caretColorError: string;
            clearColor: string;
            clearColorHover: string;
            clearColorPressed: string;
            paddingSingle: string;
            paddingMultiple: string;
            clearSize: string;
            arrowSize: string;
        }, {
            Popover: import("../../_mixins").Theme<"Popover", {
                fontSize: string;
                borderRadius: string;
                color: string;
                dividerColor: string;
                textColor: string;
                boxShadow: string;
                space: string;
                spaceArrow: string;
                arrowOffset: string;
                arrowOffsetVertical: string;
                arrowHeight: string;
                padding: string;
            }, any>;
        }>;
    }>>>;
}>> & Readonly<{}>, {
    readonly options: TreeSelectOption[];
    readonly to: string | boolean | HTMLElement;
    readonly disabled: boolean | undefined;
    readonly show: boolean | undefined;
    readonly separator: string;
    readonly multiple: boolean;
    readonly loading: boolean;
    readonly bordered: boolean;
    readonly placement: FollowerPlacement;
    readonly keyField: string;
    readonly labelField: string;
    readonly virtualScroll: boolean;
    readonly checkable: boolean;
    readonly defaultShow: boolean;
    readonly filterable: boolean;
    readonly clearable: boolean;
    readonly clearFilterAfterSelect: boolean;
    readonly defaultValue: string | number | (string | number)[] | null;
    readonly consistentMenuWidth: boolean;
    readonly childrenField: string;
    readonly defaultExpandAll: boolean;
    readonly allowCheckingNotLoaded: boolean;
    readonly cascade: boolean;
    readonly indent: number;
    readonly checkStrategy: CheckStrategy;
    readonly disabledField: string;
    readonly leafOnly: boolean;
    readonly showPath: boolean;
    readonly defaultExpandedKeys: Key[];
}, SlotsType<TreeSelectSlots>, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
