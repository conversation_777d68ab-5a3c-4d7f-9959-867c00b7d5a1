import type { ExtractPublicPropTypes } from '../../_utils';
export declare const ulProps: {
    readonly alignText: BooleanConstructor;
    readonly theme: import("vue").PropType<import("../../_mixins").Theme<"Typography", {
        aTextColor: string;
        blockquoteTextColor: string;
        blockquotePrefixColor: string;
        blockquoteLineHeight: string;
        blockquoteFontSize: string;
        codeBorderRadius: string;
        liTextColor: string;
        liLineHeight: string;
        liFontSize: string;
        hrColor: string;
        headerFontWeight: string;
        headerTextColor: string;
        pTextColor: string;
        pTextColor1Depth: string;
        pTextColor2Depth: string;
        pTextColor3Depth: string;
        pLineHeight: string;
        pFontSize: string;
        headerBarColor: string;
        headerBarColorPrimary: string;
        headerBarColorInfo: string;
        headerBarColorError: string;
        headerBarColorWarning: string;
        headerBarColorSuccess: string;
        textColor: string;
        textColor1Depth: string;
        textColor2Depth: string;
        textColor3Depth: string;
        textColorPrimary: string;
        textColorInfo: string;
        textColorSuccess: string;
        textColorWarning: string;
        textColorError: string;
        codeTextColor: string;
        codeColor: string;
        codeBorder: string;
        headerFontSize1: string;
        headerFontSize2: string;
        headerFontSize3: string;
        headerFontSize4: string;
        headerFontSize5: string;
        headerFontSize6: string;
        headerMargin1: string;
        headerMargin2: string;
        headerMargin3: string;
        headerMargin4: string;
        headerMargin5: string;
        headerMargin6: string;
        headerPrefixWidth1: string;
        headerPrefixWidth2: string;
        headerPrefixWidth3: string;
        headerPrefixWidth4: string;
        headerPrefixWidth5: string;
        headerPrefixWidth6: string;
        headerBarWidth1: string;
        headerBarWidth2: string;
        headerBarWidth3: string;
        headerBarWidth4: string;
        headerBarWidth5: string;
        headerBarWidth6: string;
        pMargin: string;
        liMargin: string;
        olPadding: string;
        ulPadding: string;
    }, any>>;
    readonly themeOverrides: import("vue").PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Typography", {
        aTextColor: string;
        blockquoteTextColor: string;
        blockquotePrefixColor: string;
        blockquoteLineHeight: string;
        blockquoteFontSize: string;
        codeBorderRadius: string;
        liTextColor: string;
        liLineHeight: string;
        liFontSize: string;
        hrColor: string;
        headerFontWeight: string;
        headerTextColor: string;
        pTextColor: string;
        pTextColor1Depth: string;
        pTextColor2Depth: string;
        pTextColor3Depth: string;
        pLineHeight: string;
        pFontSize: string;
        headerBarColor: string;
        headerBarColorPrimary: string;
        headerBarColorInfo: string;
        headerBarColorError: string;
        headerBarColorWarning: string;
        headerBarColorSuccess: string;
        textColor: string;
        textColor1Depth: string;
        textColor2Depth: string;
        textColor3Depth: string;
        textColorPrimary: string;
        textColorInfo: string;
        textColorSuccess: string;
        textColorWarning: string;
        textColorError: string;
        codeTextColor: string;
        codeColor: string;
        codeBorder: string;
        headerFontSize1: string;
        headerFontSize2: string;
        headerFontSize3: string;
        headerFontSize4: string;
        headerFontSize5: string;
        headerFontSize6: string;
        headerMargin1: string;
        headerMargin2: string;
        headerMargin3: string;
        headerMargin4: string;
        headerMargin5: string;
        headerMargin6: string;
        headerPrefixWidth1: string;
        headerPrefixWidth2: string;
        headerPrefixWidth3: string;
        headerPrefixWidth4: string;
        headerPrefixWidth5: string;
        headerPrefixWidth6: string;
        headerBarWidth1: string;
        headerBarWidth2: string;
        headerBarWidth3: string;
        headerBarWidth4: string;
        headerBarWidth5: string;
        headerBarWidth6: string;
        pMargin: string;
        liMargin: string;
        olPadding: string;
        ulPadding: string;
    }, any>>>;
    readonly builtinThemeOverrides: import("vue").PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Typography", {
        aTextColor: string;
        blockquoteTextColor: string;
        blockquotePrefixColor: string;
        blockquoteLineHeight: string;
        blockquoteFontSize: string;
        codeBorderRadius: string;
        liTextColor: string;
        liLineHeight: string;
        liFontSize: string;
        hrColor: string;
        headerFontWeight: string;
        headerTextColor: string;
        pTextColor: string;
        pTextColor1Depth: string;
        pTextColor2Depth: string;
        pTextColor3Depth: string;
        pLineHeight: string;
        pFontSize: string;
        headerBarColor: string;
        headerBarColorPrimary: string;
        headerBarColorInfo: string;
        headerBarColorError: string;
        headerBarColorWarning: string;
        headerBarColorSuccess: string;
        textColor: string;
        textColor1Depth: string;
        textColor2Depth: string;
        textColor3Depth: string;
        textColorPrimary: string;
        textColorInfo: string;
        textColorSuccess: string;
        textColorWarning: string;
        textColorError: string;
        codeTextColor: string;
        codeColor: string;
        codeBorder: string;
        headerFontSize1: string;
        headerFontSize2: string;
        headerFontSize3: string;
        headerFontSize4: string;
        headerFontSize5: string;
        headerFontSize6: string;
        headerMargin1: string;
        headerMargin2: string;
        headerMargin3: string;
        headerMargin4: string;
        headerMargin5: string;
        headerMargin6: string;
        headerPrefixWidth1: string;
        headerPrefixWidth2: string;
        headerPrefixWidth3: string;
        headerPrefixWidth4: string;
        headerPrefixWidth5: string;
        headerPrefixWidth6: string;
        headerBarWidth1: string;
        headerBarWidth2: string;
        headerBarWidth3: string;
        headerBarWidth4: string;
        headerBarWidth5: string;
        headerBarWidth6: string;
        pMargin: string;
        liMargin: string;
        olPadding: string;
        ulPadding: string;
    }, any>>>;
};
export type UlProps = ExtractPublicPropTypes<typeof ulProps>;
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    readonly alignText: BooleanConstructor;
    readonly theme: import("vue").PropType<import("../../_mixins").Theme<"Typography", {
        aTextColor: string;
        blockquoteTextColor: string;
        blockquotePrefixColor: string;
        blockquoteLineHeight: string;
        blockquoteFontSize: string;
        codeBorderRadius: string;
        liTextColor: string;
        liLineHeight: string;
        liFontSize: string;
        hrColor: string;
        headerFontWeight: string;
        headerTextColor: string;
        pTextColor: string;
        pTextColor1Depth: string;
        pTextColor2Depth: string;
        pTextColor3Depth: string;
        pLineHeight: string;
        pFontSize: string;
        headerBarColor: string;
        headerBarColorPrimary: string;
        headerBarColorInfo: string;
        headerBarColorError: string;
        headerBarColorWarning: string;
        headerBarColorSuccess: string;
        textColor: string;
        textColor1Depth: string;
        textColor2Depth: string;
        textColor3Depth: string;
        textColorPrimary: string;
        textColorInfo: string;
        textColorSuccess: string;
        textColorWarning: string;
        textColorError: string;
        codeTextColor: string;
        codeColor: string;
        codeBorder: string;
        headerFontSize1: string;
        headerFontSize2: string;
        headerFontSize3: string;
        headerFontSize4: string;
        headerFontSize5: string;
        headerFontSize6: string;
        headerMargin1: string;
        headerMargin2: string;
        headerMargin3: string;
        headerMargin4: string;
        headerMargin5: string;
        headerMargin6: string;
        headerPrefixWidth1: string;
        headerPrefixWidth2: string;
        headerPrefixWidth3: string;
        headerPrefixWidth4: string;
        headerPrefixWidth5: string;
        headerPrefixWidth6: string;
        headerBarWidth1: string;
        headerBarWidth2: string;
        headerBarWidth3: string;
        headerBarWidth4: string;
        headerBarWidth5: string;
        headerBarWidth6: string;
        pMargin: string;
        liMargin: string;
        olPadding: string;
        ulPadding: string;
    }, any>>;
    readonly themeOverrides: import("vue").PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Typography", {
        aTextColor: string;
        blockquoteTextColor: string;
        blockquotePrefixColor: string;
        blockquoteLineHeight: string;
        blockquoteFontSize: string;
        codeBorderRadius: string;
        liTextColor: string;
        liLineHeight: string;
        liFontSize: string;
        hrColor: string;
        headerFontWeight: string;
        headerTextColor: string;
        pTextColor: string;
        pTextColor1Depth: string;
        pTextColor2Depth: string;
        pTextColor3Depth: string;
        pLineHeight: string;
        pFontSize: string;
        headerBarColor: string;
        headerBarColorPrimary: string;
        headerBarColorInfo: string;
        headerBarColorError: string;
        headerBarColorWarning: string;
        headerBarColorSuccess: string;
        textColor: string;
        textColor1Depth: string;
        textColor2Depth: string;
        textColor3Depth: string;
        textColorPrimary: string;
        textColorInfo: string;
        textColorSuccess: string;
        textColorWarning: string;
        textColorError: string;
        codeTextColor: string;
        codeColor: string;
        codeBorder: string;
        headerFontSize1: string;
        headerFontSize2: string;
        headerFontSize3: string;
        headerFontSize4: string;
        headerFontSize5: string;
        headerFontSize6: string;
        headerMargin1: string;
        headerMargin2: string;
        headerMargin3: string;
        headerMargin4: string;
        headerMargin5: string;
        headerMargin6: string;
        headerPrefixWidth1: string;
        headerPrefixWidth2: string;
        headerPrefixWidth3: string;
        headerPrefixWidth4: string;
        headerPrefixWidth5: string;
        headerPrefixWidth6: string;
        headerBarWidth1: string;
        headerBarWidth2: string;
        headerBarWidth3: string;
        headerBarWidth4: string;
        headerBarWidth5: string;
        headerBarWidth6: string;
        pMargin: string;
        liMargin: string;
        olPadding: string;
        ulPadding: string;
    }, any>>>;
    readonly builtinThemeOverrides: import("vue").PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Typography", {
        aTextColor: string;
        blockquoteTextColor: string;
        blockquotePrefixColor: string;
        blockquoteLineHeight: string;
        blockquoteFontSize: string;
        codeBorderRadius: string;
        liTextColor: string;
        liLineHeight: string;
        liFontSize: string;
        hrColor: string;
        headerFontWeight: string;
        headerTextColor: string;
        pTextColor: string;
        pTextColor1Depth: string;
        pTextColor2Depth: string;
        pTextColor3Depth: string;
        pLineHeight: string;
        pFontSize: string;
        headerBarColor: string;
        headerBarColorPrimary: string;
        headerBarColorInfo: string;
        headerBarColorError: string;
        headerBarColorWarning: string;
        headerBarColorSuccess: string;
        textColor: string;
        textColor1Depth: string;
        textColor2Depth: string;
        textColor3Depth: string;
        textColorPrimary: string;
        textColorInfo: string;
        textColorSuccess: string;
        textColorWarning: string;
        textColorError: string;
        codeTextColor: string;
        codeColor: string;
        codeBorder: string;
        headerFontSize1: string;
        headerFontSize2: string;
        headerFontSize3: string;
        headerFontSize4: string;
        headerFontSize5: string;
        headerFontSize6: string;
        headerMargin1: string;
        headerMargin2: string;
        headerMargin3: string;
        headerMargin4: string;
        headerMargin5: string;
        headerMargin6: string;
        headerPrefixWidth1: string;
        headerPrefixWidth2: string;
        headerPrefixWidth3: string;
        headerPrefixWidth4: string;
        headerPrefixWidth5: string;
        headerPrefixWidth6: string;
        headerBarWidth1: string;
        headerBarWidth2: string;
        headerBarWidth3: string;
        headerBarWidth4: string;
        headerBarWidth5: string;
        headerBarWidth6: string;
        pMargin: string;
        liMargin: string;
        olPadding: string;
        ulPadding: string;
    }, any>>>;
}>, {
    mergedClsPrefix: import("vue").Ref<string, string>;
    cssVars: import("vue").ComputedRef<{
        '--n-bezier': string;
        '--n-font-size': string;
        '--n-line-height': string;
        '--n-text-color': string;
        '--n-li-margin': string;
        '--n-ol-padding': string;
        '--n-ul-padding': string;
    }> | undefined;
    themeClass: import("vue").Ref<string, string> | undefined;
    onRender: (() => void) | undefined;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    readonly alignText: BooleanConstructor;
    readonly theme: import("vue").PropType<import("../../_mixins").Theme<"Typography", {
        aTextColor: string;
        blockquoteTextColor: string;
        blockquotePrefixColor: string;
        blockquoteLineHeight: string;
        blockquoteFontSize: string;
        codeBorderRadius: string;
        liTextColor: string;
        liLineHeight: string;
        liFontSize: string;
        hrColor: string;
        headerFontWeight: string;
        headerTextColor: string;
        pTextColor: string;
        pTextColor1Depth: string;
        pTextColor2Depth: string;
        pTextColor3Depth: string;
        pLineHeight: string;
        pFontSize: string;
        headerBarColor: string;
        headerBarColorPrimary: string;
        headerBarColorInfo: string;
        headerBarColorError: string;
        headerBarColorWarning: string;
        headerBarColorSuccess: string;
        textColor: string;
        textColor1Depth: string;
        textColor2Depth: string;
        textColor3Depth: string;
        textColorPrimary: string;
        textColorInfo: string;
        textColorSuccess: string;
        textColorWarning: string;
        textColorError: string;
        codeTextColor: string;
        codeColor: string;
        codeBorder: string;
        headerFontSize1: string;
        headerFontSize2: string;
        headerFontSize3: string;
        headerFontSize4: string;
        headerFontSize5: string;
        headerFontSize6: string;
        headerMargin1: string;
        headerMargin2: string;
        headerMargin3: string;
        headerMargin4: string;
        headerMargin5: string;
        headerMargin6: string;
        headerPrefixWidth1: string;
        headerPrefixWidth2: string;
        headerPrefixWidth3: string;
        headerPrefixWidth4: string;
        headerPrefixWidth5: string;
        headerPrefixWidth6: string;
        headerBarWidth1: string;
        headerBarWidth2: string;
        headerBarWidth3: string;
        headerBarWidth4: string;
        headerBarWidth5: string;
        headerBarWidth6: string;
        pMargin: string;
        liMargin: string;
        olPadding: string;
        ulPadding: string;
    }, any>>;
    readonly themeOverrides: import("vue").PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Typography", {
        aTextColor: string;
        blockquoteTextColor: string;
        blockquotePrefixColor: string;
        blockquoteLineHeight: string;
        blockquoteFontSize: string;
        codeBorderRadius: string;
        liTextColor: string;
        liLineHeight: string;
        liFontSize: string;
        hrColor: string;
        headerFontWeight: string;
        headerTextColor: string;
        pTextColor: string;
        pTextColor1Depth: string;
        pTextColor2Depth: string;
        pTextColor3Depth: string;
        pLineHeight: string;
        pFontSize: string;
        headerBarColor: string;
        headerBarColorPrimary: string;
        headerBarColorInfo: string;
        headerBarColorError: string;
        headerBarColorWarning: string;
        headerBarColorSuccess: string;
        textColor: string;
        textColor1Depth: string;
        textColor2Depth: string;
        textColor3Depth: string;
        textColorPrimary: string;
        textColorInfo: string;
        textColorSuccess: string;
        textColorWarning: string;
        textColorError: string;
        codeTextColor: string;
        codeColor: string;
        codeBorder: string;
        headerFontSize1: string;
        headerFontSize2: string;
        headerFontSize3: string;
        headerFontSize4: string;
        headerFontSize5: string;
        headerFontSize6: string;
        headerMargin1: string;
        headerMargin2: string;
        headerMargin3: string;
        headerMargin4: string;
        headerMargin5: string;
        headerMargin6: string;
        headerPrefixWidth1: string;
        headerPrefixWidth2: string;
        headerPrefixWidth3: string;
        headerPrefixWidth4: string;
        headerPrefixWidth5: string;
        headerPrefixWidth6: string;
        headerBarWidth1: string;
        headerBarWidth2: string;
        headerBarWidth3: string;
        headerBarWidth4: string;
        headerBarWidth5: string;
        headerBarWidth6: string;
        pMargin: string;
        liMargin: string;
        olPadding: string;
        ulPadding: string;
    }, any>>>;
    readonly builtinThemeOverrides: import("vue").PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Typography", {
        aTextColor: string;
        blockquoteTextColor: string;
        blockquotePrefixColor: string;
        blockquoteLineHeight: string;
        blockquoteFontSize: string;
        codeBorderRadius: string;
        liTextColor: string;
        liLineHeight: string;
        liFontSize: string;
        hrColor: string;
        headerFontWeight: string;
        headerTextColor: string;
        pTextColor: string;
        pTextColor1Depth: string;
        pTextColor2Depth: string;
        pTextColor3Depth: string;
        pLineHeight: string;
        pFontSize: string;
        headerBarColor: string;
        headerBarColorPrimary: string;
        headerBarColorInfo: string;
        headerBarColorError: string;
        headerBarColorWarning: string;
        headerBarColorSuccess: string;
        textColor: string;
        textColor1Depth: string;
        textColor2Depth: string;
        textColor3Depth: string;
        textColorPrimary: string;
        textColorInfo: string;
        textColorSuccess: string;
        textColorWarning: string;
        textColorError: string;
        codeTextColor: string;
        codeColor: string;
        codeBorder: string;
        headerFontSize1: string;
        headerFontSize2: string;
        headerFontSize3: string;
        headerFontSize4: string;
        headerFontSize5: string;
        headerFontSize6: string;
        headerMargin1: string;
        headerMargin2: string;
        headerMargin3: string;
        headerMargin4: string;
        headerMargin5: string;
        headerMargin6: string;
        headerPrefixWidth1: string;
        headerPrefixWidth2: string;
        headerPrefixWidth3: string;
        headerPrefixWidth4: string;
        headerPrefixWidth5: string;
        headerPrefixWidth6: string;
        headerBarWidth1: string;
        headerBarWidth2: string;
        headerBarWidth3: string;
        headerBarWidth4: string;
        headerBarWidth5: string;
        headerBarWidth6: string;
        pMargin: string;
        liMargin: string;
        olPadding: string;
        ulPadding: string;
    }, any>>>;
}>> & Readonly<{}>, {
    readonly alignText: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
