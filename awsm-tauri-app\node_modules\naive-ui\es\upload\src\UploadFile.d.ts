import type { ImageInst } from '../../image/src/Image';
import type { ListType } from './interface';
import type { UploadSettledFileInfo } from './public-types';
import { type PropType } from 'vue';
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    clsPrefix: {
        type: StringConstructor;
        required: true;
    };
    file: {
        type: PropType<UploadSettledFileInfo>;
        required: true;
    };
    listType: {
        type: PropType<ListType>;
        required: true;
    };
    index: {
        type: NumberConstructor;
        required: true;
    };
}>, {
    mergedTheme: import("vue").Ref<{
        common: import("../..").ThemeCommonVars;
        self: {
            fontSize: string;
            lineHeight: string;
            borderRadius: string;
            draggerColor: string;
            draggerBorder: string;
            draggerBorderHover: string;
            itemColorHover: string;
            itemColorHoverError: string;
            itemTextColor: string;
            itemTextColorError: string;
            itemTextColorSuccess: string;
            itemIconColor: string;
            itemDisabledOpacity: string;
            itemBorderImageCardError: string;
            itemBorderImageCard: string;
        };
        peers: {
            Button: import("../../_mixins/use-theme").Theme<"Button", {
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                borderRadiusTiny: string;
                borderRadiusSmall: string;
                borderRadiusMedium: string;
                borderRadiusLarge: string;
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                opacityDisabled: string;
                colorOpacitySecondary: string;
                colorOpacitySecondaryHover: string;
                colorOpacitySecondaryPressed: string;
                colorSecondary: string;
                colorSecondaryHover: string;
                colorSecondaryPressed: string;
                colorTertiary: string;
                colorTertiaryHover: string;
                colorTertiaryPressed: string;
                colorQuaternary: string;
                colorQuaternaryHover: string;
                colorQuaternaryPressed: string;
                color: string;
                colorHover: string;
                colorPressed: string;
                colorFocus: string;
                colorDisabled: string;
                textColor: string;
                textColorTertiary: string;
                textColorHover: string;
                textColorPressed: string;
                textColorFocus: string;
                textColorDisabled: string;
                textColorText: string;
                textColorTextHover: string;
                textColorTextPressed: string;
                textColorTextFocus: string;
                textColorTextDisabled: string;
                textColorGhost: string;
                textColorGhostHover: string;
                textColorGhostPressed: string;
                textColorGhostFocus: string;
                textColorGhostDisabled: string;
                border: string;
                borderHover: string;
                borderPressed: string;
                borderFocus: string;
                borderDisabled: string;
                rippleColor: string;
                colorPrimary: string;
                colorHoverPrimary: string;
                colorPressedPrimary: string;
                colorFocusPrimary: string;
                colorDisabledPrimary: string;
                textColorPrimary: string;
                textColorHoverPrimary: string;
                textColorPressedPrimary: string;
                textColorFocusPrimary: string;
                textColorDisabledPrimary: string;
                textColorTextPrimary: string;
                textColorTextHoverPrimary: string;
                textColorTextPressedPrimary: string;
                textColorTextFocusPrimary: string;
                textColorTextDisabledPrimary: string;
                textColorGhostPrimary: string;
                textColorGhostHoverPrimary: string;
                textColorGhostPressedPrimary: string;
                textColorGhostFocusPrimary: string;
                textColorGhostDisabledPrimary: string;
                borderPrimary: string;
                borderHoverPrimary: string;
                borderPressedPrimary: string;
                borderFocusPrimary: string;
                borderDisabledPrimary: string;
                rippleColorPrimary: string;
                colorInfo: string;
                colorHoverInfo: string;
                colorPressedInfo: string;
                colorFocusInfo: string;
                colorDisabledInfo: string;
                textColorInfo: string;
                textColorHoverInfo: string;
                textColorPressedInfo: string;
                textColorFocusInfo: string;
                textColorDisabledInfo: string;
                textColorTextInfo: string;
                textColorTextHoverInfo: string;
                textColorTextPressedInfo: string;
                textColorTextFocusInfo: string;
                textColorTextDisabledInfo: string;
                textColorGhostInfo: string;
                textColorGhostHoverInfo: string;
                textColorGhostPressedInfo: string;
                textColorGhostFocusInfo: string;
                textColorGhostDisabledInfo: string;
                borderInfo: string;
                borderHoverInfo: string;
                borderPressedInfo: string;
                borderFocusInfo: string;
                borderDisabledInfo: string;
                rippleColorInfo: string;
                colorSuccess: string;
                colorHoverSuccess: string;
                colorPressedSuccess: string;
                colorFocusSuccess: string;
                colorDisabledSuccess: string;
                textColorSuccess: string;
                textColorHoverSuccess: string;
                textColorPressedSuccess: string;
                textColorFocusSuccess: string;
                textColorDisabledSuccess: string;
                textColorTextSuccess: string;
                textColorTextHoverSuccess: string;
                textColorTextPressedSuccess: string;
                textColorTextFocusSuccess: string;
                textColorTextDisabledSuccess: string;
                textColorGhostSuccess: string;
                textColorGhostHoverSuccess: string;
                textColorGhostPressedSuccess: string;
                textColorGhostFocusSuccess: string;
                textColorGhostDisabledSuccess: string;
                borderSuccess: string;
                borderHoverSuccess: string;
                borderPressedSuccess: string;
                borderFocusSuccess: string;
                borderDisabledSuccess: string;
                rippleColorSuccess: string;
                colorWarning: string;
                colorHoverWarning: string;
                colorPressedWarning: string;
                colorFocusWarning: string;
                colorDisabledWarning: string;
                textColorWarning: string;
                textColorHoverWarning: string;
                textColorPressedWarning: string;
                textColorFocusWarning: string;
                textColorDisabledWarning: string;
                textColorTextWarning: string;
                textColorTextHoverWarning: string;
                textColorTextPressedWarning: string;
                textColorTextFocusWarning: string;
                textColorTextDisabledWarning: string;
                textColorGhostWarning: string;
                textColorGhostHoverWarning: string;
                textColorGhostPressedWarning: string;
                textColorGhostFocusWarning: string;
                textColorGhostDisabledWarning: string;
                borderWarning: string;
                borderHoverWarning: string;
                borderPressedWarning: string;
                borderFocusWarning: string;
                borderDisabledWarning: string;
                rippleColorWarning: string;
                colorError: string;
                colorHoverError: string;
                colorPressedError: string;
                colorFocusError: string;
                colorDisabledError: string;
                textColorError: string;
                textColorHoverError: string;
                textColorPressedError: string;
                textColorFocusError: string;
                textColorDisabledError: string;
                textColorTextError: string;
                textColorTextHoverError: string;
                textColorTextPressedError: string;
                textColorTextFocusError: string;
                textColorTextDisabledError: string;
                textColorGhostError: string;
                textColorGhostHoverError: string;
                textColorGhostPressedError: string;
                textColorGhostFocusError: string;
                textColorGhostDisabledError: string;
                borderError: string;
                borderHoverError: string;
                borderPressedError: string;
                borderFocusError: string;
                borderDisabledError: string;
                rippleColorError: string;
                waveOpacity: string;
                fontWeight: string;
                fontWeightStrong: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingRoundTiny: string;
                paddingRoundSmall: string;
                paddingRoundMedium: string;
                paddingRoundLarge: string;
                iconMarginTiny: string;
                iconMarginSmall: string;
                iconMarginMedium: string;
                iconMarginLarge: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                rippleDuration: string;
            }, any>;
            Progress: import("../../_mixins/use-theme").Theme<"Progress", {
                fontSize: string;
                fontSizeCircle: string;
                fontWeightCircle: string;
                railColor: string;
                railHeight: string;
                iconSizeCircle: string;
                iconSizeLine: string;
                iconColor: string;
                iconColorInfo: string;
                iconColorSuccess: string;
                iconColorWarning: string;
                iconColorError: string;
                textColorCircle: string;
                textColorLineInner: string;
                textColorLineOuter: string;
                fillColor: string;
                fillColorInfo: string;
                fillColorSuccess: string;
                fillColorWarning: string;
                fillColorError: string;
                lineBgProcessing: string;
            }, any>;
        };
        peerOverrides: {
            Button?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
            Progress?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
        };
    }, {
        common: import("../..").ThemeCommonVars;
        self: {
            fontSize: string;
            lineHeight: string;
            borderRadius: string;
            draggerColor: string;
            draggerBorder: string;
            draggerBorderHover: string;
            itemColorHover: string;
            itemColorHoverError: string;
            itemTextColor: string;
            itemTextColorError: string;
            itemTextColorSuccess: string;
            itemIconColor: string;
            itemDisabledOpacity: string;
            itemBorderImageCardError: string;
            itemBorderImageCard: string;
        };
        peers: {
            Button: import("../../_mixins/use-theme").Theme<"Button", {
                heightTiny: string;
                heightSmall: string;
                heightMedium: string;
                heightLarge: string;
                borderRadiusTiny: string;
                borderRadiusSmall: string;
                borderRadiusMedium: string;
                borderRadiusLarge: string;
                fontSizeTiny: string;
                fontSizeSmall: string;
                fontSizeMedium: string;
                fontSizeLarge: string;
                opacityDisabled: string;
                colorOpacitySecondary: string;
                colorOpacitySecondaryHover: string;
                colorOpacitySecondaryPressed: string;
                colorSecondary: string;
                colorSecondaryHover: string;
                colorSecondaryPressed: string;
                colorTertiary: string;
                colorTertiaryHover: string;
                colorTertiaryPressed: string;
                colorQuaternary: string;
                colorQuaternaryHover: string;
                colorQuaternaryPressed: string;
                color: string;
                colorHover: string;
                colorPressed: string;
                colorFocus: string;
                colorDisabled: string;
                textColor: string;
                textColorTertiary: string;
                textColorHover: string;
                textColorPressed: string;
                textColorFocus: string;
                textColorDisabled: string;
                textColorText: string;
                textColorTextHover: string;
                textColorTextPressed: string;
                textColorTextFocus: string;
                textColorTextDisabled: string;
                textColorGhost: string;
                textColorGhostHover: string;
                textColorGhostPressed: string;
                textColorGhostFocus: string;
                textColorGhostDisabled: string;
                border: string;
                borderHover: string;
                borderPressed: string;
                borderFocus: string;
                borderDisabled: string;
                rippleColor: string;
                colorPrimary: string;
                colorHoverPrimary: string;
                colorPressedPrimary: string;
                colorFocusPrimary: string;
                colorDisabledPrimary: string;
                textColorPrimary: string;
                textColorHoverPrimary: string;
                textColorPressedPrimary: string;
                textColorFocusPrimary: string;
                textColorDisabledPrimary: string;
                textColorTextPrimary: string;
                textColorTextHoverPrimary: string;
                textColorTextPressedPrimary: string;
                textColorTextFocusPrimary: string;
                textColorTextDisabledPrimary: string;
                textColorGhostPrimary: string;
                textColorGhostHoverPrimary: string;
                textColorGhostPressedPrimary: string;
                textColorGhostFocusPrimary: string;
                textColorGhostDisabledPrimary: string;
                borderPrimary: string;
                borderHoverPrimary: string;
                borderPressedPrimary: string;
                borderFocusPrimary: string;
                borderDisabledPrimary: string;
                rippleColorPrimary: string;
                colorInfo: string;
                colorHoverInfo: string;
                colorPressedInfo: string;
                colorFocusInfo: string;
                colorDisabledInfo: string;
                textColorInfo: string;
                textColorHoverInfo: string;
                textColorPressedInfo: string;
                textColorFocusInfo: string;
                textColorDisabledInfo: string;
                textColorTextInfo: string;
                textColorTextHoverInfo: string;
                textColorTextPressedInfo: string;
                textColorTextFocusInfo: string;
                textColorTextDisabledInfo: string;
                textColorGhostInfo: string;
                textColorGhostHoverInfo: string;
                textColorGhostPressedInfo: string;
                textColorGhostFocusInfo: string;
                textColorGhostDisabledInfo: string;
                borderInfo: string;
                borderHoverInfo: string;
                borderPressedInfo: string;
                borderFocusInfo: string;
                borderDisabledInfo: string;
                rippleColorInfo: string;
                colorSuccess: string;
                colorHoverSuccess: string;
                colorPressedSuccess: string;
                colorFocusSuccess: string;
                colorDisabledSuccess: string;
                textColorSuccess: string;
                textColorHoverSuccess: string;
                textColorPressedSuccess: string;
                textColorFocusSuccess: string;
                textColorDisabledSuccess: string;
                textColorTextSuccess: string;
                textColorTextHoverSuccess: string;
                textColorTextPressedSuccess: string;
                textColorTextFocusSuccess: string;
                textColorTextDisabledSuccess: string;
                textColorGhostSuccess: string;
                textColorGhostHoverSuccess: string;
                textColorGhostPressedSuccess: string;
                textColorGhostFocusSuccess: string;
                textColorGhostDisabledSuccess: string;
                borderSuccess: string;
                borderHoverSuccess: string;
                borderPressedSuccess: string;
                borderFocusSuccess: string;
                borderDisabledSuccess: string;
                rippleColorSuccess: string;
                colorWarning: string;
                colorHoverWarning: string;
                colorPressedWarning: string;
                colorFocusWarning: string;
                colorDisabledWarning: string;
                textColorWarning: string;
                textColorHoverWarning: string;
                textColorPressedWarning: string;
                textColorFocusWarning: string;
                textColorDisabledWarning: string;
                textColorTextWarning: string;
                textColorTextHoverWarning: string;
                textColorTextPressedWarning: string;
                textColorTextFocusWarning: string;
                textColorTextDisabledWarning: string;
                textColorGhostWarning: string;
                textColorGhostHoverWarning: string;
                textColorGhostPressedWarning: string;
                textColorGhostFocusWarning: string;
                textColorGhostDisabledWarning: string;
                borderWarning: string;
                borderHoverWarning: string;
                borderPressedWarning: string;
                borderFocusWarning: string;
                borderDisabledWarning: string;
                rippleColorWarning: string;
                colorError: string;
                colorHoverError: string;
                colorPressedError: string;
                colorFocusError: string;
                colorDisabledError: string;
                textColorError: string;
                textColorHoverError: string;
                textColorPressedError: string;
                textColorFocusError: string;
                textColorDisabledError: string;
                textColorTextError: string;
                textColorTextHoverError: string;
                textColorTextPressedError: string;
                textColorTextFocusError: string;
                textColorTextDisabledError: string;
                textColorGhostError: string;
                textColorGhostHoverError: string;
                textColorGhostPressedError: string;
                textColorGhostFocusError: string;
                textColorGhostDisabledError: string;
                borderError: string;
                borderHoverError: string;
                borderPressedError: string;
                borderFocusError: string;
                borderDisabledError: string;
                rippleColorError: string;
                waveOpacity: string;
                fontWeight: string;
                fontWeightStrong: string;
                paddingTiny: string;
                paddingSmall: string;
                paddingMedium: string;
                paddingLarge: string;
                paddingRoundTiny: string;
                paddingRoundSmall: string;
                paddingRoundMedium: string;
                paddingRoundLarge: string;
                iconMarginTiny: string;
                iconMarginSmall: string;
                iconMarginMedium: string;
                iconMarginLarge: string;
                iconSizeTiny: string;
                iconSizeSmall: string;
                iconSizeMedium: string;
                iconSizeLarge: string;
                rippleDuration: string;
            }, any>;
            Progress: import("../../_mixins/use-theme").Theme<"Progress", {
                fontSize: string;
                fontSizeCircle: string;
                fontWeightCircle: string;
                railColor: string;
                railHeight: string;
                iconSizeCircle: string;
                iconSizeLine: string;
                iconColor: string;
                iconColorInfo: string;
                iconColorSuccess: string;
                iconColorWarning: string;
                iconColorError: string;
                textColorCircle: string;
                textColorLineInner: string;
                textColorLineOuter: string;
                fillColor: string;
                fillColorInfo: string;
                fillColorSuccess: string;
                fillColorWarning: string;
                fillColorError: string;
                lineBgProcessing: string;
            }, any>;
        };
        peerOverrides: {
            Button?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
            Progress?: {
                peers?: {
                    [x: string]: any;
                } | undefined;
            } | undefined;
        };
    }>;
    progressStatus: import("vue").ComputedRef<"info" | "success" | "error">;
    buttonType: import("vue").ComputedRef<"error" | undefined>;
    showProgress: import("vue").ComputedRef<boolean>;
    disabled: import("vue").Ref<boolean, boolean>;
    showCancelButton: import("vue").ComputedRef<boolean>;
    showRemoveButton: import("vue").ComputedRef<boolean>;
    showDownloadButton: import("vue").ComputedRef<boolean>;
    showRetryButton: import("vue").ComputedRef<boolean>;
    showPreviewButton: import("vue").ComputedRef<boolean | "" | null>;
    mergedThumbnailUrl: import("vue").ComputedRef<string | null>;
    shouldUseThumbnailUrl: import("vue").Ref<import("./interface").ShouldUseThumbnailUrl, import("./interface").ShouldUseThumbnailUrl>;
    renderIcon: import("vue").Ref<import("./interface").RenderIcon | undefined, import("./interface").RenderIcon | undefined>;
    imageRef: import("vue").Ref<{
        click: () => void;
    } | null, ImageInst | {
        click: () => void;
    } | null>;
    handleRemoveOrCancelClick: (e: MouseEvent) => void;
    handleDownloadClick: (e: MouseEvent) => void;
    handleRetryClick: () => Promise<void>;
    handlePreviewClick: (e: MouseEvent) => void;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    clsPrefix: {
        type: StringConstructor;
        required: true;
    };
    file: {
        type: PropType<UploadSettledFileInfo>;
        required: true;
    };
    listType: {
        type: PropType<ListType>;
        required: true;
    };
    index: {
        type: NumberConstructor;
        required: true;
    };
}>> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
