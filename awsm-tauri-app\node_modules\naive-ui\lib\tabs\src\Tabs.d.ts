import type { ExtractPublicPropTypes, MaybeArray } from '../../_utils';
import type { Addable, OnBeforeLeave, OnClose, OnUpdateValue, TabsType } from './interface';
import { type ComponentPublicInstance, type CSSProperties, type ExtractPropTypes, nextTick, type PropType, type SlotsType, type VNode } from 'vue';
import { type VXScrollInst } from 'vueuc';
export declare const tabsProps: {
    readonly value: PropType<string | number>;
    readonly defaultValue: PropType<string | number>;
    readonly trigger: {
        readonly type: PropType<"click" | "hover">;
        readonly default: "click";
    };
    readonly type: {
        readonly type: PropType<TabsType>;
        readonly default: "bar";
    };
    readonly closable: BooleanConstructor;
    readonly justifyContent: PropType<"space-between" | "space-around" | "space-evenly" | "center" | "start" | "end">;
    readonly size: {
        readonly type: PropType<"small" | "medium" | "large">;
        readonly default: "medium";
    };
    readonly placement: {
        readonly type: PropType<"top" | "left" | "right" | "bottom">;
        readonly default: "top";
    };
    readonly tabStyle: PropType<string | CSSProperties>;
    readonly tabClass: StringConstructor;
    readonly addTabStyle: PropType<string | CSSProperties>;
    readonly addTabClass: StringConstructor;
    readonly barWidth: NumberConstructor;
    readonly paneClass: StringConstructor;
    readonly paneStyle: PropType<string | CSSProperties>;
    readonly paneWrapperClass: StringConstructor;
    readonly paneWrapperStyle: PropType<string | CSSProperties>;
    readonly addable: PropType<Addable>;
    readonly tabsPadding: {
        readonly type: NumberConstructor;
        readonly default: 0;
    };
    readonly animated: BooleanConstructor;
    readonly onBeforeLeave: PropType<OnBeforeLeave>;
    readonly onAdd: PropType<() => void>;
    readonly 'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    readonly onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    readonly onClose: PropType<MaybeArray<OnClose>>;
    readonly labelSize: PropType<"small" | "medium" | "large">;
    readonly activeName: PropType<string | number>;
    readonly onActiveNameChange: PropType<MaybeArray<(value: string & number) => void>>;
    readonly theme: PropType<import("../../_mixins").Theme<"Tabs", {
        colorSegment: string;
        tabFontSizeCard: string;
        tabTextColorLine: string;
        tabTextColorActiveLine: string;
        tabTextColorHoverLine: string;
        tabTextColorDisabledLine: string;
        tabTextColorSegment: string;
        tabTextColorActiveSegment: string;
        tabTextColorHoverSegment: string;
        tabTextColorDisabledSegment: string;
        tabTextColorBar: string;
        tabTextColorActiveBar: string;
        tabTextColorHoverBar: string;
        tabTextColorDisabledBar: string;
        tabTextColorCard: string;
        tabTextColorHoverCard: string;
        tabTextColorActiveCard: string;
        tabTextColorDisabledCard: string;
        barColor: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        tabColor: string;
        tabColorSegment: string;
        tabBorderColor: string;
        tabFontWeightActive: string;
        tabFontWeight: string;
        tabBorderRadius: string;
        paneTextColor: string;
        fontWeightStrong: string;
        tabFontSizeSmall: string;
        tabFontSizeMedium: string;
        tabFontSizeLarge: string;
        tabGapSmallLine: string;
        tabGapMediumLine: string;
        tabGapLargeLine: string;
        tabGapSmallLineVertical: string;
        tabGapMediumLineVertical: string;
        tabGapLargeLineVertical: string;
        tabPaddingSmallLine: string;
        tabPaddingMediumLine: string;
        tabPaddingLargeLine: string;
        tabPaddingVerticalSmallLine: string;
        tabPaddingVerticalMediumLine: string;
        tabPaddingVerticalLargeLine: string;
        tabGapSmallBar: string;
        tabGapMediumBar: string;
        tabGapLargeBar: string;
        tabGapSmallBarVertical: string;
        tabGapMediumBarVertical: string;
        tabGapLargeBarVertical: string;
        tabPaddingSmallBar: string;
        tabPaddingMediumBar: string;
        tabPaddingLargeBar: string;
        tabPaddingVerticalSmallBar: string;
        tabPaddingVerticalMediumBar: string;
        tabPaddingVerticalLargeBar: string;
        tabGapSmallCard: string;
        tabGapMediumCard: string;
        tabGapLargeCard: string;
        tabGapSmallCardVertical: string;
        tabGapMediumCardVertical: string;
        tabGapLargeCardVertical: string;
        tabPaddingSmallCard: string;
        tabPaddingMediumCard: string;
        tabPaddingLargeCard: string;
        tabPaddingSmallSegment: string;
        tabPaddingMediumSegment: string;
        tabPaddingLargeSegment: string;
        tabPaddingVerticalLargeSegment: string;
        tabPaddingVerticalSmallCard: string;
        tabPaddingVerticalMediumCard: string;
        tabPaddingVerticalLargeCard: string;
        tabPaddingVerticalSmallSegment: string;
        tabPaddingVerticalMediumSegment: string;
        tabGapSmallSegment: string;
        tabGapMediumSegment: string;
        tabGapLargeSegment: string;
        tabGapSmallSegmentVertical: string;
        tabGapMediumSegmentVertical: string;
        tabGapLargeSegmentVertical: string;
        panePaddingSmall: string;
        panePaddingMedium: string;
        panePaddingLarge: string;
        closeSize: string;
        closeIconSize: string;
    }, any>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Tabs", {
        colorSegment: string;
        tabFontSizeCard: string;
        tabTextColorLine: string;
        tabTextColorActiveLine: string;
        tabTextColorHoverLine: string;
        tabTextColorDisabledLine: string;
        tabTextColorSegment: string;
        tabTextColorActiveSegment: string;
        tabTextColorHoverSegment: string;
        tabTextColorDisabledSegment: string;
        tabTextColorBar: string;
        tabTextColorActiveBar: string;
        tabTextColorHoverBar: string;
        tabTextColorDisabledBar: string;
        tabTextColorCard: string;
        tabTextColorHoverCard: string;
        tabTextColorActiveCard: string;
        tabTextColorDisabledCard: string;
        barColor: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        tabColor: string;
        tabColorSegment: string;
        tabBorderColor: string;
        tabFontWeightActive: string;
        tabFontWeight: string;
        tabBorderRadius: string;
        paneTextColor: string;
        fontWeightStrong: string;
        tabFontSizeSmall: string;
        tabFontSizeMedium: string;
        tabFontSizeLarge: string;
        tabGapSmallLine: string;
        tabGapMediumLine: string;
        tabGapLargeLine: string;
        tabGapSmallLineVertical: string;
        tabGapMediumLineVertical: string;
        tabGapLargeLineVertical: string;
        tabPaddingSmallLine: string;
        tabPaddingMediumLine: string;
        tabPaddingLargeLine: string;
        tabPaddingVerticalSmallLine: string;
        tabPaddingVerticalMediumLine: string;
        tabPaddingVerticalLargeLine: string;
        tabGapSmallBar: string;
        tabGapMediumBar: string;
        tabGapLargeBar: string;
        tabGapSmallBarVertical: string;
        tabGapMediumBarVertical: string;
        tabGapLargeBarVertical: string;
        tabPaddingSmallBar: string;
        tabPaddingMediumBar: string;
        tabPaddingLargeBar: string;
        tabPaddingVerticalSmallBar: string;
        tabPaddingVerticalMediumBar: string;
        tabPaddingVerticalLargeBar: string;
        tabGapSmallCard: string;
        tabGapMediumCard: string;
        tabGapLargeCard: string;
        tabGapSmallCardVertical: string;
        tabGapMediumCardVertical: string;
        tabGapLargeCardVertical: string;
        tabPaddingSmallCard: string;
        tabPaddingMediumCard: string;
        tabPaddingLargeCard: string;
        tabPaddingSmallSegment: string;
        tabPaddingMediumSegment: string;
        tabPaddingLargeSegment: string;
        tabPaddingVerticalLargeSegment: string;
        tabPaddingVerticalSmallCard: string;
        tabPaddingVerticalMediumCard: string;
        tabPaddingVerticalLargeCard: string;
        tabPaddingVerticalSmallSegment: string;
        tabPaddingVerticalMediumSegment: string;
        tabGapSmallSegment: string;
        tabGapMediumSegment: string;
        tabGapLargeSegment: string;
        tabGapSmallSegmentVertical: string;
        tabGapMediumSegmentVertical: string;
        tabGapLargeSegmentVertical: string;
        panePaddingSmall: string;
        panePaddingMedium: string;
        panePaddingLarge: string;
        closeSize: string;
        closeIconSize: string;
    }, any>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Tabs", {
        colorSegment: string;
        tabFontSizeCard: string;
        tabTextColorLine: string;
        tabTextColorActiveLine: string;
        tabTextColorHoverLine: string;
        tabTextColorDisabledLine: string;
        tabTextColorSegment: string;
        tabTextColorActiveSegment: string;
        tabTextColorHoverSegment: string;
        tabTextColorDisabledSegment: string;
        tabTextColorBar: string;
        tabTextColorActiveBar: string;
        tabTextColorHoverBar: string;
        tabTextColorDisabledBar: string;
        tabTextColorCard: string;
        tabTextColorHoverCard: string;
        tabTextColorActiveCard: string;
        tabTextColorDisabledCard: string;
        barColor: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        tabColor: string;
        tabColorSegment: string;
        tabBorderColor: string;
        tabFontWeightActive: string;
        tabFontWeight: string;
        tabBorderRadius: string;
        paneTextColor: string;
        fontWeightStrong: string;
        tabFontSizeSmall: string;
        tabFontSizeMedium: string;
        tabFontSizeLarge: string;
        tabGapSmallLine: string;
        tabGapMediumLine: string;
        tabGapLargeLine: string;
        tabGapSmallLineVertical: string;
        tabGapMediumLineVertical: string;
        tabGapLargeLineVertical: string;
        tabPaddingSmallLine: string;
        tabPaddingMediumLine: string;
        tabPaddingLargeLine: string;
        tabPaddingVerticalSmallLine: string;
        tabPaddingVerticalMediumLine: string;
        tabPaddingVerticalLargeLine: string;
        tabGapSmallBar: string;
        tabGapMediumBar: string;
        tabGapLargeBar: string;
        tabGapSmallBarVertical: string;
        tabGapMediumBarVertical: string;
        tabGapLargeBarVertical: string;
        tabPaddingSmallBar: string;
        tabPaddingMediumBar: string;
        tabPaddingLargeBar: string;
        tabPaddingVerticalSmallBar: string;
        tabPaddingVerticalMediumBar: string;
        tabPaddingVerticalLargeBar: string;
        tabGapSmallCard: string;
        tabGapMediumCard: string;
        tabGapLargeCard: string;
        tabGapSmallCardVertical: string;
        tabGapMediumCardVertical: string;
        tabGapLargeCardVertical: string;
        tabPaddingSmallCard: string;
        tabPaddingMediumCard: string;
        tabPaddingLargeCard: string;
        tabPaddingSmallSegment: string;
        tabPaddingMediumSegment: string;
        tabPaddingLargeSegment: string;
        tabPaddingVerticalLargeSegment: string;
        tabPaddingVerticalSmallCard: string;
        tabPaddingVerticalMediumCard: string;
        tabPaddingVerticalLargeCard: string;
        tabPaddingVerticalSmallSegment: string;
        tabPaddingVerticalMediumSegment: string;
        tabGapSmallSegment: string;
        tabGapMediumSegment: string;
        tabGapLargeSegment: string;
        tabGapSmallSegmentVertical: string;
        tabGapMediumSegmentVertical: string;
        tabGapLargeSegmentVertical: string;
        panePaddingSmall: string;
        panePaddingMedium: string;
        panePaddingLarge: string;
        closeSize: string;
        closeIconSize: string;
    }, any>>>;
};
export type TabsProps = ExtractPublicPropTypes<typeof tabsProps>;
export interface TabsSlots {
    default?: () => VNode[];
    prefix?: () => VNode[];
    suffix?: () => VNode[];
}
declare const _default: import("vue").DefineComponent<ExtractPropTypes<{
    readonly value: PropType<string | number>;
    readonly defaultValue: PropType<string | number>;
    readonly trigger: {
        readonly type: PropType<"click" | "hover">;
        readonly default: "click";
    };
    readonly type: {
        readonly type: PropType<TabsType>;
        readonly default: "bar";
    };
    readonly closable: BooleanConstructor;
    readonly justifyContent: PropType<"space-between" | "space-around" | "space-evenly" | "center" | "start" | "end">;
    readonly size: {
        readonly type: PropType<"small" | "medium" | "large">;
        readonly default: "medium";
    };
    readonly placement: {
        readonly type: PropType<"top" | "left" | "right" | "bottom">;
        readonly default: "top";
    };
    readonly tabStyle: PropType<string | CSSProperties>;
    readonly tabClass: StringConstructor;
    readonly addTabStyle: PropType<string | CSSProperties>;
    readonly addTabClass: StringConstructor;
    readonly barWidth: NumberConstructor;
    readonly paneClass: StringConstructor;
    readonly paneStyle: PropType<string | CSSProperties>;
    readonly paneWrapperClass: StringConstructor;
    readonly paneWrapperStyle: PropType<string | CSSProperties>;
    readonly addable: PropType<Addable>;
    readonly tabsPadding: {
        readonly type: NumberConstructor;
        readonly default: 0;
    };
    readonly animated: BooleanConstructor;
    readonly onBeforeLeave: PropType<OnBeforeLeave>;
    readonly onAdd: PropType<() => void>;
    readonly 'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    readonly onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    readonly onClose: PropType<MaybeArray<OnClose>>;
    readonly labelSize: PropType<"small" | "medium" | "large">;
    readonly activeName: PropType<string | number>;
    readonly onActiveNameChange: PropType<MaybeArray<(value: string & number) => void>>;
    readonly theme: PropType<import("../../_mixins").Theme<"Tabs", {
        colorSegment: string;
        tabFontSizeCard: string;
        tabTextColorLine: string;
        tabTextColorActiveLine: string;
        tabTextColorHoverLine: string;
        tabTextColorDisabledLine: string;
        tabTextColorSegment: string;
        tabTextColorActiveSegment: string;
        tabTextColorHoverSegment: string;
        tabTextColorDisabledSegment: string;
        tabTextColorBar: string;
        tabTextColorActiveBar: string;
        tabTextColorHoverBar: string;
        tabTextColorDisabledBar: string;
        tabTextColorCard: string;
        tabTextColorHoverCard: string;
        tabTextColorActiveCard: string;
        tabTextColorDisabledCard: string;
        barColor: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        tabColor: string;
        tabColorSegment: string;
        tabBorderColor: string;
        tabFontWeightActive: string;
        tabFontWeight: string;
        tabBorderRadius: string;
        paneTextColor: string;
        fontWeightStrong: string;
        tabFontSizeSmall: string;
        tabFontSizeMedium: string;
        tabFontSizeLarge: string;
        tabGapSmallLine: string;
        tabGapMediumLine: string;
        tabGapLargeLine: string;
        tabGapSmallLineVertical: string;
        tabGapMediumLineVertical: string;
        tabGapLargeLineVertical: string;
        tabPaddingSmallLine: string;
        tabPaddingMediumLine: string;
        tabPaddingLargeLine: string;
        tabPaddingVerticalSmallLine: string;
        tabPaddingVerticalMediumLine: string;
        tabPaddingVerticalLargeLine: string;
        tabGapSmallBar: string;
        tabGapMediumBar: string;
        tabGapLargeBar: string;
        tabGapSmallBarVertical: string;
        tabGapMediumBarVertical: string;
        tabGapLargeBarVertical: string;
        tabPaddingSmallBar: string;
        tabPaddingMediumBar: string;
        tabPaddingLargeBar: string;
        tabPaddingVerticalSmallBar: string;
        tabPaddingVerticalMediumBar: string;
        tabPaddingVerticalLargeBar: string;
        tabGapSmallCard: string;
        tabGapMediumCard: string;
        tabGapLargeCard: string;
        tabGapSmallCardVertical: string;
        tabGapMediumCardVertical: string;
        tabGapLargeCardVertical: string;
        tabPaddingSmallCard: string;
        tabPaddingMediumCard: string;
        tabPaddingLargeCard: string;
        tabPaddingSmallSegment: string;
        tabPaddingMediumSegment: string;
        tabPaddingLargeSegment: string;
        tabPaddingVerticalLargeSegment: string;
        tabPaddingVerticalSmallCard: string;
        tabPaddingVerticalMediumCard: string;
        tabPaddingVerticalLargeCard: string;
        tabPaddingVerticalSmallSegment: string;
        tabPaddingVerticalMediumSegment: string;
        tabGapSmallSegment: string;
        tabGapMediumSegment: string;
        tabGapLargeSegment: string;
        tabGapSmallSegmentVertical: string;
        tabGapMediumSegmentVertical: string;
        tabGapLargeSegmentVertical: string;
        panePaddingSmall: string;
        panePaddingMedium: string;
        panePaddingLarge: string;
        closeSize: string;
        closeIconSize: string;
    }, any>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Tabs", {
        colorSegment: string;
        tabFontSizeCard: string;
        tabTextColorLine: string;
        tabTextColorActiveLine: string;
        tabTextColorHoverLine: string;
        tabTextColorDisabledLine: string;
        tabTextColorSegment: string;
        tabTextColorActiveSegment: string;
        tabTextColorHoverSegment: string;
        tabTextColorDisabledSegment: string;
        tabTextColorBar: string;
        tabTextColorActiveBar: string;
        tabTextColorHoverBar: string;
        tabTextColorDisabledBar: string;
        tabTextColorCard: string;
        tabTextColorHoverCard: string;
        tabTextColorActiveCard: string;
        tabTextColorDisabledCard: string;
        barColor: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        tabColor: string;
        tabColorSegment: string;
        tabBorderColor: string;
        tabFontWeightActive: string;
        tabFontWeight: string;
        tabBorderRadius: string;
        paneTextColor: string;
        fontWeightStrong: string;
        tabFontSizeSmall: string;
        tabFontSizeMedium: string;
        tabFontSizeLarge: string;
        tabGapSmallLine: string;
        tabGapMediumLine: string;
        tabGapLargeLine: string;
        tabGapSmallLineVertical: string;
        tabGapMediumLineVertical: string;
        tabGapLargeLineVertical: string;
        tabPaddingSmallLine: string;
        tabPaddingMediumLine: string;
        tabPaddingLargeLine: string;
        tabPaddingVerticalSmallLine: string;
        tabPaddingVerticalMediumLine: string;
        tabPaddingVerticalLargeLine: string;
        tabGapSmallBar: string;
        tabGapMediumBar: string;
        tabGapLargeBar: string;
        tabGapSmallBarVertical: string;
        tabGapMediumBarVertical: string;
        tabGapLargeBarVertical: string;
        tabPaddingSmallBar: string;
        tabPaddingMediumBar: string;
        tabPaddingLargeBar: string;
        tabPaddingVerticalSmallBar: string;
        tabPaddingVerticalMediumBar: string;
        tabPaddingVerticalLargeBar: string;
        tabGapSmallCard: string;
        tabGapMediumCard: string;
        tabGapLargeCard: string;
        tabGapSmallCardVertical: string;
        tabGapMediumCardVertical: string;
        tabGapLargeCardVertical: string;
        tabPaddingSmallCard: string;
        tabPaddingMediumCard: string;
        tabPaddingLargeCard: string;
        tabPaddingSmallSegment: string;
        tabPaddingMediumSegment: string;
        tabPaddingLargeSegment: string;
        tabPaddingVerticalLargeSegment: string;
        tabPaddingVerticalSmallCard: string;
        tabPaddingVerticalMediumCard: string;
        tabPaddingVerticalLargeCard: string;
        tabPaddingVerticalSmallSegment: string;
        tabPaddingVerticalMediumSegment: string;
        tabGapSmallSegment: string;
        tabGapMediumSegment: string;
        tabGapLargeSegment: string;
        tabGapSmallSegmentVertical: string;
        tabGapMediumSegmentVertical: string;
        tabGapLargeSegmentVertical: string;
        panePaddingSmall: string;
        panePaddingMedium: string;
        panePaddingLarge: string;
        closeSize: string;
        closeIconSize: string;
    }, any>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Tabs", {
        colorSegment: string;
        tabFontSizeCard: string;
        tabTextColorLine: string;
        tabTextColorActiveLine: string;
        tabTextColorHoverLine: string;
        tabTextColorDisabledLine: string;
        tabTextColorSegment: string;
        tabTextColorActiveSegment: string;
        tabTextColorHoverSegment: string;
        tabTextColorDisabledSegment: string;
        tabTextColorBar: string;
        tabTextColorActiveBar: string;
        tabTextColorHoverBar: string;
        tabTextColorDisabledBar: string;
        tabTextColorCard: string;
        tabTextColorHoverCard: string;
        tabTextColorActiveCard: string;
        tabTextColorDisabledCard: string;
        barColor: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        tabColor: string;
        tabColorSegment: string;
        tabBorderColor: string;
        tabFontWeightActive: string;
        tabFontWeight: string;
        tabBorderRadius: string;
        paneTextColor: string;
        fontWeightStrong: string;
        tabFontSizeSmall: string;
        tabFontSizeMedium: string;
        tabFontSizeLarge: string;
        tabGapSmallLine: string;
        tabGapMediumLine: string;
        tabGapLargeLine: string;
        tabGapSmallLineVertical: string;
        tabGapMediumLineVertical: string;
        tabGapLargeLineVertical: string;
        tabPaddingSmallLine: string;
        tabPaddingMediumLine: string;
        tabPaddingLargeLine: string;
        tabPaddingVerticalSmallLine: string;
        tabPaddingVerticalMediumLine: string;
        tabPaddingVerticalLargeLine: string;
        tabGapSmallBar: string;
        tabGapMediumBar: string;
        tabGapLargeBar: string;
        tabGapSmallBarVertical: string;
        tabGapMediumBarVertical: string;
        tabGapLargeBarVertical: string;
        tabPaddingSmallBar: string;
        tabPaddingMediumBar: string;
        tabPaddingLargeBar: string;
        tabPaddingVerticalSmallBar: string;
        tabPaddingVerticalMediumBar: string;
        tabPaddingVerticalLargeBar: string;
        tabGapSmallCard: string;
        tabGapMediumCard: string;
        tabGapLargeCard: string;
        tabGapSmallCardVertical: string;
        tabGapMediumCardVertical: string;
        tabGapLargeCardVertical: string;
        tabPaddingSmallCard: string;
        tabPaddingMediumCard: string;
        tabPaddingLargeCard: string;
        tabPaddingSmallSegment: string;
        tabPaddingMediumSegment: string;
        tabPaddingLargeSegment: string;
        tabPaddingVerticalLargeSegment: string;
        tabPaddingVerticalSmallCard: string;
        tabPaddingVerticalMediumCard: string;
        tabPaddingVerticalLargeCard: string;
        tabPaddingVerticalSmallSegment: string;
        tabPaddingVerticalMediumSegment: string;
        tabGapSmallSegment: string;
        tabGapMediumSegment: string;
        tabGapLargeSegment: string;
        tabGapSmallSegmentVertical: string;
        tabGapMediumSegmentVertical: string;
        tabGapLargeSegmentVertical: string;
        panePaddingSmall: string;
        panePaddingMedium: string;
        panePaddingLarge: string;
        closeSize: string;
        closeIconSize: string;
    }, any>>>;
}>, {
    syncBarPosition: () => void;
    mergedClsPrefix: import("vue").Ref<string, string>;
    mergedValue: import("vue").ComputedRef<string | number | null>;
    renderedNames: Set<NonNullable<string | number>>;
    segmentCapsuleElRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    tabsPaneWrapperRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    tabsElRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    barElRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    addTabInstRef: import("vue").Ref<ComponentPublicInstance | null, ComponentPublicInstance | null>;
    xScrollInstRef: import("vue").Ref<(VXScrollInst & {
        $: import("vue").ComponentInternalInstance;
        $data: {};
        $props: {};
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        };
        $slots: Readonly<{
            [name: string]: import("vue").Slot<any> | undefined;
        }>;
        $root: ComponentPublicInstance | null;
        $parent: ComponentPublicInstance | null;
        $host: Element | null;
        $emit: (event: string, ...args: any[]) => void;
        $el: any;
        $options: import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}, {}, string, {}, {}, {}, string, import("vue").ComponentProvideOptions> & {
            beforeCreate?: (() => void) | (() => void)[];
            created?: (() => void) | (() => void)[];
            beforeMount?: (() => void) | (() => void)[];
            mounted?: (() => void) | (() => void)[];
            beforeUpdate?: (() => void) | (() => void)[];
            updated?: (() => void) | (() => void)[];
            activated?: (() => void) | (() => void)[];
            deactivated?: (() => void) | (() => void)[];
            beforeDestroy?: (() => void) | (() => void)[];
            beforeUnmount?: (() => void) | (() => void)[];
            destroyed?: (() => void) | (() => void)[];
            unmounted?: (() => void) | (() => void)[];
            renderTracked?: ((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[];
            renderTriggered?: ((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[];
            errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
        };
        $forceUpdate: () => void;
        $nextTick: typeof nextTick;
        $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, import("@vue/reactivity").OnCleanup]) => any : (...args: [any, any, import("@vue/reactivity").OnCleanup]) => any, options?: import("vue").WatchOptions): import("vue").WatchStopHandle;
    } & Readonly<{}> & Omit<{}, never> & import("vue").ShallowUnwrapRef<{}> & {} & import("vue").ComponentCustomProperties & {}) | null, (VXScrollInst & {
        $: import("vue").ComponentInternalInstance;
        $data: {};
        $props: {};
        $attrs: {
            [x: string]: unknown;
        };
        $refs: {
            [x: string]: unknown;
        };
        $slots: Readonly<{
            [name: string]: import("vue").Slot<any> | undefined;
        }>;
        $root: ComponentPublicInstance | null;
        $parent: ComponentPublicInstance | null;
        $host: Element | null;
        $emit: (event: string, ...args: any[]) => void;
        $el: any;
        $options: import("vue").ComponentOptionsBase<any, any, any, any, any, any, any, any, any, {}, {}, string, {}, {}, {}, string, import("vue").ComponentProvideOptions> & {
            beforeCreate?: (() => void) | (() => void)[];
            created?: (() => void) | (() => void)[];
            beforeMount?: (() => void) | (() => void)[];
            mounted?: (() => void) | (() => void)[];
            beforeUpdate?: (() => void) | (() => void)[];
            updated?: (() => void) | (() => void)[];
            activated?: (() => void) | (() => void)[];
            deactivated?: (() => void) | (() => void)[];
            beforeDestroy?: (() => void) | (() => void)[];
            beforeUnmount?: (() => void) | (() => void)[];
            destroyed?: (() => void) | (() => void)[];
            unmounted?: (() => void) | (() => void)[];
            renderTracked?: ((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[];
            renderTriggered?: ((e: import("vue").DebuggerEvent) => void) | ((e: import("vue").DebuggerEvent) => void)[];
            errorCaptured?: ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void) | ((err: unknown, instance: ComponentPublicInstance | null, info: string) => boolean | void)[];
        };
        $forceUpdate: () => void;
        $nextTick: typeof nextTick;
        $watch<T extends string | ((...args: any) => any)>(source: T, cb: T extends (...args: any) => infer R ? (...args: [R, R, import("@vue/reactivity").OnCleanup]) => any : (...args: [any, any, import("@vue/reactivity").OnCleanup]) => any, options?: import("vue").WatchOptions): import("vue").WatchStopHandle;
    } & Readonly<{}> & Omit<{}, never> & import("vue").ShallowUnwrapRef<{}> & {} & import("vue").ComponentCustomProperties & {}) | null>;
    scrollWrapperElRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    addTabFixed: import("vue").Ref<boolean, boolean>;
    tabWrapperStyle: import("vue").ComputedRef<{
        display: string;
        justifyContent: "start" | "end" | "space-around" | "space-between" | "space-evenly" | "center";
    } | undefined>;
    handleNavResize: import("lodash").DebouncedFuncLeading<(entry: ResizeObserverEntry) => void>;
    mergedSize: import("vue").ComputedRef<"small" | "medium" | "large">;
    handleScroll: import("lodash").DebouncedFuncLeading<(e: Event) => void>;
    handleTabsResize: import("lodash").DebouncedFuncLeading<(entry: ResizeObserverEntry) => void>;
    cssVars: import("vue").ComputedRef<{
        '--n-bezier': string;
        '--n-color-segment': string;
        '--n-bar-color': string;
        '--n-tab-font-size': string;
        '--n-tab-text-color': string;
        '--n-tab-text-color-active': string;
        '--n-tab-text-color-disabled': string;
        '--n-tab-text-color-hover': string;
        '--n-pane-text-color': string;
        '--n-tab-border-color': string;
        '--n-tab-border-radius': string;
        '--n-close-size': string;
        '--n-close-icon-size': string;
        '--n-close-color-hover': string;
        '--n-close-color-pressed': string;
        '--n-close-border-radius': string;
        '--n-close-icon-color': string;
        '--n-close-icon-color-hover': string;
        '--n-close-icon-color-pressed': string;
        '--n-tab-color': string;
        '--n-tab-font-weight': string;
        '--n-tab-font-weight-active': string;
        '--n-tab-padding': string;
        '--n-tab-padding-vertical': string;
        '--n-tab-gap': string;
        '--n-tab-gap-vertical': string;
        '--n-pane-padding-left': string;
        '--n-pane-padding-right': string;
        '--n-pane-padding-top': string;
        '--n-pane-padding-bottom': string;
        '--n-font-weight-strong': string;
        '--n-tab-color-segment': string;
    }> | undefined;
    themeClass: import("vue").Ref<string, string> | undefined;
    animationDirection: import("vue").Ref<"next" | "prev", "next" | "prev">;
    renderNameListRef: {
        value: Array<string | number>;
    };
    yScrollElRef: import("vue").Ref<HTMLElement | null, HTMLElement | null>;
    handleSegmentResize: () => void;
    onAnimationBeforeLeave: (el: HTMLElement) => void;
    onAnimationEnter: (el: HTMLElement) => void;
    onAnimationAfterEnter: () => void;
    onRender: (() => void) | undefined;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<ExtractPropTypes<{
    readonly value: PropType<string | number>;
    readonly defaultValue: PropType<string | number>;
    readonly trigger: {
        readonly type: PropType<"click" | "hover">;
        readonly default: "click";
    };
    readonly type: {
        readonly type: PropType<TabsType>;
        readonly default: "bar";
    };
    readonly closable: BooleanConstructor;
    readonly justifyContent: PropType<"space-between" | "space-around" | "space-evenly" | "center" | "start" | "end">;
    readonly size: {
        readonly type: PropType<"small" | "medium" | "large">;
        readonly default: "medium";
    };
    readonly placement: {
        readonly type: PropType<"top" | "left" | "right" | "bottom">;
        readonly default: "top";
    };
    readonly tabStyle: PropType<string | CSSProperties>;
    readonly tabClass: StringConstructor;
    readonly addTabStyle: PropType<string | CSSProperties>;
    readonly addTabClass: StringConstructor;
    readonly barWidth: NumberConstructor;
    readonly paneClass: StringConstructor;
    readonly paneStyle: PropType<string | CSSProperties>;
    readonly paneWrapperClass: StringConstructor;
    readonly paneWrapperStyle: PropType<string | CSSProperties>;
    readonly addable: PropType<Addable>;
    readonly tabsPadding: {
        readonly type: NumberConstructor;
        readonly default: 0;
    };
    readonly animated: BooleanConstructor;
    readonly onBeforeLeave: PropType<OnBeforeLeave>;
    readonly onAdd: PropType<() => void>;
    readonly 'onUpdate:value': PropType<MaybeArray<OnUpdateValue>>;
    readonly onUpdateValue: PropType<MaybeArray<OnUpdateValue>>;
    readonly onClose: PropType<MaybeArray<OnClose>>;
    readonly labelSize: PropType<"small" | "medium" | "large">;
    readonly activeName: PropType<string | number>;
    readonly onActiveNameChange: PropType<MaybeArray<(value: string & number) => void>>;
    readonly theme: PropType<import("../../_mixins").Theme<"Tabs", {
        colorSegment: string;
        tabFontSizeCard: string;
        tabTextColorLine: string;
        tabTextColorActiveLine: string;
        tabTextColorHoverLine: string;
        tabTextColorDisabledLine: string;
        tabTextColorSegment: string;
        tabTextColorActiveSegment: string;
        tabTextColorHoverSegment: string;
        tabTextColorDisabledSegment: string;
        tabTextColorBar: string;
        tabTextColorActiveBar: string;
        tabTextColorHoverBar: string;
        tabTextColorDisabledBar: string;
        tabTextColorCard: string;
        tabTextColorHoverCard: string;
        tabTextColorActiveCard: string;
        tabTextColorDisabledCard: string;
        barColor: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        tabColor: string;
        tabColorSegment: string;
        tabBorderColor: string;
        tabFontWeightActive: string;
        tabFontWeight: string;
        tabBorderRadius: string;
        paneTextColor: string;
        fontWeightStrong: string;
        tabFontSizeSmall: string;
        tabFontSizeMedium: string;
        tabFontSizeLarge: string;
        tabGapSmallLine: string;
        tabGapMediumLine: string;
        tabGapLargeLine: string;
        tabGapSmallLineVertical: string;
        tabGapMediumLineVertical: string;
        tabGapLargeLineVertical: string;
        tabPaddingSmallLine: string;
        tabPaddingMediumLine: string;
        tabPaddingLargeLine: string;
        tabPaddingVerticalSmallLine: string;
        tabPaddingVerticalMediumLine: string;
        tabPaddingVerticalLargeLine: string;
        tabGapSmallBar: string;
        tabGapMediumBar: string;
        tabGapLargeBar: string;
        tabGapSmallBarVertical: string;
        tabGapMediumBarVertical: string;
        tabGapLargeBarVertical: string;
        tabPaddingSmallBar: string;
        tabPaddingMediumBar: string;
        tabPaddingLargeBar: string;
        tabPaddingVerticalSmallBar: string;
        tabPaddingVerticalMediumBar: string;
        tabPaddingVerticalLargeBar: string;
        tabGapSmallCard: string;
        tabGapMediumCard: string;
        tabGapLargeCard: string;
        tabGapSmallCardVertical: string;
        tabGapMediumCardVertical: string;
        tabGapLargeCardVertical: string;
        tabPaddingSmallCard: string;
        tabPaddingMediumCard: string;
        tabPaddingLargeCard: string;
        tabPaddingSmallSegment: string;
        tabPaddingMediumSegment: string;
        tabPaddingLargeSegment: string;
        tabPaddingVerticalLargeSegment: string;
        tabPaddingVerticalSmallCard: string;
        tabPaddingVerticalMediumCard: string;
        tabPaddingVerticalLargeCard: string;
        tabPaddingVerticalSmallSegment: string;
        tabPaddingVerticalMediumSegment: string;
        tabGapSmallSegment: string;
        tabGapMediumSegment: string;
        tabGapLargeSegment: string;
        tabGapSmallSegmentVertical: string;
        tabGapMediumSegmentVertical: string;
        tabGapLargeSegmentVertical: string;
        panePaddingSmall: string;
        panePaddingMedium: string;
        panePaddingLarge: string;
        closeSize: string;
        closeIconSize: string;
    }, any>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Tabs", {
        colorSegment: string;
        tabFontSizeCard: string;
        tabTextColorLine: string;
        tabTextColorActiveLine: string;
        tabTextColorHoverLine: string;
        tabTextColorDisabledLine: string;
        tabTextColorSegment: string;
        tabTextColorActiveSegment: string;
        tabTextColorHoverSegment: string;
        tabTextColorDisabledSegment: string;
        tabTextColorBar: string;
        tabTextColorActiveBar: string;
        tabTextColorHoverBar: string;
        tabTextColorDisabledBar: string;
        tabTextColorCard: string;
        tabTextColorHoverCard: string;
        tabTextColorActiveCard: string;
        tabTextColorDisabledCard: string;
        barColor: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        tabColor: string;
        tabColorSegment: string;
        tabBorderColor: string;
        tabFontWeightActive: string;
        tabFontWeight: string;
        tabBorderRadius: string;
        paneTextColor: string;
        fontWeightStrong: string;
        tabFontSizeSmall: string;
        tabFontSizeMedium: string;
        tabFontSizeLarge: string;
        tabGapSmallLine: string;
        tabGapMediumLine: string;
        tabGapLargeLine: string;
        tabGapSmallLineVertical: string;
        tabGapMediumLineVertical: string;
        tabGapLargeLineVertical: string;
        tabPaddingSmallLine: string;
        tabPaddingMediumLine: string;
        tabPaddingLargeLine: string;
        tabPaddingVerticalSmallLine: string;
        tabPaddingVerticalMediumLine: string;
        tabPaddingVerticalLargeLine: string;
        tabGapSmallBar: string;
        tabGapMediumBar: string;
        tabGapLargeBar: string;
        tabGapSmallBarVertical: string;
        tabGapMediumBarVertical: string;
        tabGapLargeBarVertical: string;
        tabPaddingSmallBar: string;
        tabPaddingMediumBar: string;
        tabPaddingLargeBar: string;
        tabPaddingVerticalSmallBar: string;
        tabPaddingVerticalMediumBar: string;
        tabPaddingVerticalLargeBar: string;
        tabGapSmallCard: string;
        tabGapMediumCard: string;
        tabGapLargeCard: string;
        tabGapSmallCardVertical: string;
        tabGapMediumCardVertical: string;
        tabGapLargeCardVertical: string;
        tabPaddingSmallCard: string;
        tabPaddingMediumCard: string;
        tabPaddingLargeCard: string;
        tabPaddingSmallSegment: string;
        tabPaddingMediumSegment: string;
        tabPaddingLargeSegment: string;
        tabPaddingVerticalLargeSegment: string;
        tabPaddingVerticalSmallCard: string;
        tabPaddingVerticalMediumCard: string;
        tabPaddingVerticalLargeCard: string;
        tabPaddingVerticalSmallSegment: string;
        tabPaddingVerticalMediumSegment: string;
        tabGapSmallSegment: string;
        tabGapMediumSegment: string;
        tabGapLargeSegment: string;
        tabGapSmallSegmentVertical: string;
        tabGapMediumSegmentVertical: string;
        tabGapLargeSegmentVertical: string;
        panePaddingSmall: string;
        panePaddingMedium: string;
        panePaddingLarge: string;
        closeSize: string;
        closeIconSize: string;
    }, any>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Tabs", {
        colorSegment: string;
        tabFontSizeCard: string;
        tabTextColorLine: string;
        tabTextColorActiveLine: string;
        tabTextColorHoverLine: string;
        tabTextColorDisabledLine: string;
        tabTextColorSegment: string;
        tabTextColorActiveSegment: string;
        tabTextColorHoverSegment: string;
        tabTextColorDisabledSegment: string;
        tabTextColorBar: string;
        tabTextColorActiveBar: string;
        tabTextColorHoverBar: string;
        tabTextColorDisabledBar: string;
        tabTextColorCard: string;
        tabTextColorHoverCard: string;
        tabTextColorActiveCard: string;
        tabTextColorDisabledCard: string;
        barColor: string;
        closeIconColor: string;
        closeIconColorHover: string;
        closeIconColorPressed: string;
        closeColorHover: string;
        closeColorPressed: string;
        closeBorderRadius: string;
        tabColor: string;
        tabColorSegment: string;
        tabBorderColor: string;
        tabFontWeightActive: string;
        tabFontWeight: string;
        tabBorderRadius: string;
        paneTextColor: string;
        fontWeightStrong: string;
        tabFontSizeSmall: string;
        tabFontSizeMedium: string;
        tabFontSizeLarge: string;
        tabGapSmallLine: string;
        tabGapMediumLine: string;
        tabGapLargeLine: string;
        tabGapSmallLineVertical: string;
        tabGapMediumLineVertical: string;
        tabGapLargeLineVertical: string;
        tabPaddingSmallLine: string;
        tabPaddingMediumLine: string;
        tabPaddingLargeLine: string;
        tabPaddingVerticalSmallLine: string;
        tabPaddingVerticalMediumLine: string;
        tabPaddingVerticalLargeLine: string;
        tabGapSmallBar: string;
        tabGapMediumBar: string;
        tabGapLargeBar: string;
        tabGapSmallBarVertical: string;
        tabGapMediumBarVertical: string;
        tabGapLargeBarVertical: string;
        tabPaddingSmallBar: string;
        tabPaddingMediumBar: string;
        tabPaddingLargeBar: string;
        tabPaddingVerticalSmallBar: string;
        tabPaddingVerticalMediumBar: string;
        tabPaddingVerticalLargeBar: string;
        tabGapSmallCard: string;
        tabGapMediumCard: string;
        tabGapLargeCard: string;
        tabGapSmallCardVertical: string;
        tabGapMediumCardVertical: string;
        tabGapLargeCardVertical: string;
        tabPaddingSmallCard: string;
        tabPaddingMediumCard: string;
        tabPaddingLargeCard: string;
        tabPaddingSmallSegment: string;
        tabPaddingMediumSegment: string;
        tabPaddingLargeSegment: string;
        tabPaddingVerticalLargeSegment: string;
        tabPaddingVerticalSmallCard: string;
        tabPaddingVerticalMediumCard: string;
        tabPaddingVerticalLargeCard: string;
        tabPaddingVerticalSmallSegment: string;
        tabPaddingVerticalMediumSegment: string;
        tabGapSmallSegment: string;
        tabGapMediumSegment: string;
        tabGapLargeSegment: string;
        tabGapSmallSegmentVertical: string;
        tabGapMediumSegmentVertical: string;
        tabGapLargeSegmentVertical: string;
        panePaddingSmall: string;
        panePaddingMedium: string;
        panePaddingLarge: string;
        closeSize: string;
        closeIconSize: string;
    }, any>>>;
}>> & Readonly<{}>, {
    readonly type: TabsType;
    readonly size: "small" | "medium" | "large";
    readonly placement: "left" | "right" | "top" | "bottom";
    readonly trigger: "click" | "hover";
    readonly closable: boolean;
    readonly animated: boolean;
    readonly tabsPadding: number;
}, SlotsType<TabsSlots>, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
