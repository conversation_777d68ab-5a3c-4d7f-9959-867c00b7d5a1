import type { TimelineTheme } from '../styles';
import { type ExtractPropTypes, type PropType, type Ref } from 'vue';
import { type MergedTheme } from '../../_mixins';
import { type ExtractPublicPropTypes } from '../../_utils';
export declare const timelineProps: {
    readonly horizontal: BooleanConstructor;
    readonly itemPlacement: {
        readonly type: PropType<"left" | "right">;
        readonly default: "left";
    };
    readonly size: {
        readonly type: PropType<"medium" | "large">;
        readonly default: "medium";
    };
    readonly iconSize: NumberConstructor;
    readonly theme: PropType<import("../../_mixins").Theme<"Timeline", {
        contentFontSize: string;
        titleFontWeight: string;
        circleBorder: string;
        circleBorderInfo: string;
        circleBorderError: string;
        circleBorderSuccess: string;
        circleBorderWarning: string;
        iconColor: string;
        iconColorInfo: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorWarning: string;
        titleTextColor: string;
        contentTextColor: string;
        metaTextColor: string;
        lineColor: string;
        titleMarginMedium: string;
        titleMarginLarge: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
    }, any>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Timeline", {
        contentFontSize: string;
        titleFontWeight: string;
        circleBorder: string;
        circleBorderInfo: string;
        circleBorderError: string;
        circleBorderSuccess: string;
        circleBorderWarning: string;
        iconColor: string;
        iconColorInfo: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorWarning: string;
        titleTextColor: string;
        contentTextColor: string;
        metaTextColor: string;
        lineColor: string;
        titleMarginMedium: string;
        titleMarginLarge: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
    }, any>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Timeline", {
        contentFontSize: string;
        titleFontWeight: string;
        circleBorder: string;
        circleBorderInfo: string;
        circleBorderError: string;
        circleBorderSuccess: string;
        circleBorderWarning: string;
        iconColor: string;
        iconColorInfo: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorWarning: string;
        titleTextColor: string;
        contentTextColor: string;
        metaTextColor: string;
        lineColor: string;
        titleMarginMedium: string;
        titleMarginLarge: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
    }, any>>>;
};
export interface TimelineInjection {
    props: ExtractPropTypes<typeof timelineProps>;
    mergedThemeRef: Ref<MergedTheme<TimelineTheme>>;
    mergedClsPrefixRef: Ref<string>;
}
export declare const timelineInjectionKey: import("vue").InjectionKey<TimelineInjection>;
export type TimelineProps = ExtractPublicPropTypes<typeof timelineProps>;
declare const _default: import("vue").DefineComponent<ExtractPropTypes<{
    readonly horizontal: BooleanConstructor;
    readonly itemPlacement: {
        readonly type: PropType<"left" | "right">;
        readonly default: "left";
    };
    readonly size: {
        readonly type: PropType<"medium" | "large">;
        readonly default: "medium";
    };
    readonly iconSize: NumberConstructor;
    readonly theme: PropType<import("../../_mixins").Theme<"Timeline", {
        contentFontSize: string;
        titleFontWeight: string;
        circleBorder: string;
        circleBorderInfo: string;
        circleBorderError: string;
        circleBorderSuccess: string;
        circleBorderWarning: string;
        iconColor: string;
        iconColorInfo: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorWarning: string;
        titleTextColor: string;
        contentTextColor: string;
        metaTextColor: string;
        lineColor: string;
        titleMarginMedium: string;
        titleMarginLarge: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
    }, any>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Timeline", {
        contentFontSize: string;
        titleFontWeight: string;
        circleBorder: string;
        circleBorderInfo: string;
        circleBorderError: string;
        circleBorderSuccess: string;
        circleBorderWarning: string;
        iconColor: string;
        iconColorInfo: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorWarning: string;
        titleTextColor: string;
        contentTextColor: string;
        metaTextColor: string;
        lineColor: string;
        titleMarginMedium: string;
        titleMarginLarge: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
    }, any>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Timeline", {
        contentFontSize: string;
        titleFontWeight: string;
        circleBorder: string;
        circleBorderInfo: string;
        circleBorderError: string;
        circleBorderSuccess: string;
        circleBorderWarning: string;
        iconColor: string;
        iconColorInfo: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorWarning: string;
        titleTextColor: string;
        contentTextColor: string;
        metaTextColor: string;
        lineColor: string;
        titleMarginMedium: string;
        titleMarginLarge: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
    }, any>>>;
}>, () => JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<ExtractPropTypes<{
    readonly horizontal: BooleanConstructor;
    readonly itemPlacement: {
        readonly type: PropType<"left" | "right">;
        readonly default: "left";
    };
    readonly size: {
        readonly type: PropType<"medium" | "large">;
        readonly default: "medium";
    };
    readonly iconSize: NumberConstructor;
    readonly theme: PropType<import("../../_mixins").Theme<"Timeline", {
        contentFontSize: string;
        titleFontWeight: string;
        circleBorder: string;
        circleBorderInfo: string;
        circleBorderError: string;
        circleBorderSuccess: string;
        circleBorderWarning: string;
        iconColor: string;
        iconColorInfo: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorWarning: string;
        titleTextColor: string;
        contentTextColor: string;
        metaTextColor: string;
        lineColor: string;
        titleMarginMedium: string;
        titleMarginLarge: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
    }, any>>;
    readonly themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Timeline", {
        contentFontSize: string;
        titleFontWeight: string;
        circleBorder: string;
        circleBorderInfo: string;
        circleBorderError: string;
        circleBorderSuccess: string;
        circleBorderWarning: string;
        iconColor: string;
        iconColorInfo: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorWarning: string;
        titleTextColor: string;
        contentTextColor: string;
        metaTextColor: string;
        lineColor: string;
        titleMarginMedium: string;
        titleMarginLarge: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
    }, any>>>;
    readonly builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Timeline", {
        contentFontSize: string;
        titleFontWeight: string;
        circleBorder: string;
        circleBorderInfo: string;
        circleBorderError: string;
        circleBorderSuccess: string;
        circleBorderWarning: string;
        iconColor: string;
        iconColorInfo: string;
        iconColorError: string;
        iconColorSuccess: string;
        iconColorWarning: string;
        titleTextColor: string;
        contentTextColor: string;
        metaTextColor: string;
        lineColor: string;
        titleMarginMedium: string;
        titleMarginLarge: string;
        titleFontSizeMedium: string;
        titleFontSizeLarge: string;
        iconSizeMedium: string;
        iconSizeLarge: string;
    }, any>>>;
}>> & Readonly<{}>, {
    readonly size: "medium" | "large";
    readonly horizontal: boolean;
    readonly itemPlacement: "left" | "right";
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
