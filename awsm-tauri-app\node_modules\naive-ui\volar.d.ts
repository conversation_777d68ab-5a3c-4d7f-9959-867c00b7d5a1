// Auto generated component declarations
declare module 'vue' {
  export interface GlobalComponents {
    NA: (typeof import('naive-ui'))['NA']
    NAffix: (typeof import('naive-ui'))['NAffix']
    NAlert: (typeof import('naive-ui'))['NAlert']
    NAnchor: (typeof import('naive-ui'))['NAnchor']
    NAnchorLink: (typeof import('naive-ui'))['NAnchorLink']
    NAutoComplete: (typeof import('naive-ui'))['NAutoComplete']
    NAvatar: (typeof import('naive-ui'))['NAvatar']
    NAvatarGroup: (typeof import('naive-ui'))['NAvatarGroup']
    NBackTop: (typeof import('naive-ui'))['NBackTop']
    NBadge: (typeof import('naive-ui'))['NBadge']
    NBlockquote: (typeof import('naive-ui'))['NBlockquote']
    NBreadcrumb: (typeof import('naive-ui'))['NBreadcrumb']
    NBreadcrumbItem: (typeof import('naive-ui'))['NBreadcrumbItem']
    NButton: (typeof import('naive-ui'))['NButton']
    NButtonGroup: (typeof import('naive-ui'))['NButtonGroup']
    NCalendar: (typeof import('naive-ui'))['NCalendar']
    NCard: (typeof import('naive-ui'))['NCard']
    NCarousel: (typeof import('naive-ui'))['NCarousel']
    NCarouselItem: (typeof import('naive-ui'))['NCarouselItem']
    NCascader: (typeof import('naive-ui'))['NCascader']
    NCheckbox: (typeof import('naive-ui'))['NCheckbox']
    NCheckboxGroup: (typeof import('naive-ui'))['NCheckboxGroup']
    NCode: (typeof import('naive-ui'))['NCode']
    NCol: (typeof import('naive-ui'))['NCol']
    NCollapse: (typeof import('naive-ui'))['NCollapse']
    NCollapseItem: (typeof import('naive-ui'))['NCollapseItem']
    NCollapseTransition: (typeof import('naive-ui'))['NCollapseTransition']
    NColorPicker: (typeof import('naive-ui'))['NColorPicker']
    NConfigProvider: (typeof import('naive-ui'))['NConfigProvider']
    NCountdown: (typeof import('naive-ui'))['NCountdown']
    NDataTable: (typeof import('naive-ui'))['NDataTable']
    NDatePicker: (typeof import('naive-ui'))['NDatePicker']
    NDescriptions: (typeof import('naive-ui'))['NDescriptions']
    NDescriptionsItem: (typeof import('naive-ui'))['NDescriptionsItem']
    NDialog: (typeof import('naive-ui'))['NDialog']
    NDialogProvider: (typeof import('naive-ui'))['NDialogProvider']
    NDivider: (typeof import('naive-ui'))['NDivider']
    NDrawer: (typeof import('naive-ui'))['NDrawer']
    NDrawerContent: (typeof import('naive-ui'))['NDrawerContent']
    NDropdown: (typeof import('naive-ui'))['NDropdown']
    NDynamicInput: (typeof import('naive-ui'))['NDynamicInput']
    NDynamicTags: (typeof import('naive-ui'))['NDynamicTags']
    NEl: (typeof import('naive-ui'))['NEl']
    NElement: (typeof import('naive-ui'))['NElement']
    NEllipsis: (typeof import('naive-ui'))['NEllipsis']
    NEmpty: (typeof import('naive-ui'))['NEmpty']
    NEquation: (typeof import('naive-ui'))['NEquation']
    NFlex: (typeof import('naive-ui'))['NFlex']
    NFloatButton: (typeof import('naive-ui'))['NFloatButton']
    NFloatButtonGroup: (typeof import('naive-ui'))['NFloatButtonGroup']
    NForm: (typeof import('naive-ui'))['NForm']
    NFormItem: (typeof import('naive-ui'))['NFormItem']
    NFormItemCol: (typeof import('naive-ui'))['NFormItemCol']
    NFormItemGi: (typeof import('naive-ui'))['NFormItemGi']
    NFormItemGridItem: (typeof import('naive-ui'))['NFormItemGridItem']
    NFormItemRow: (typeof import('naive-ui'))['NFormItemRow']
    NGi: (typeof import('naive-ui'))['NGi']
    NGlobalStyle: (typeof import('naive-ui'))['NGlobalStyle']
    NGradientText: (typeof import('naive-ui'))['NGradientText']
    NGrid: (typeof import('naive-ui'))['NGrid']
    NGridItem: (typeof import('naive-ui'))['NGridItem']
    NH1: (typeof import('naive-ui'))['NH1']
    NH2: (typeof import('naive-ui'))['NH2']
    NH3: (typeof import('naive-ui'))['NH3']
    NH4: (typeof import('naive-ui'))['NH4']
    NH5: (typeof import('naive-ui'))['NH5']
    NH6: (typeof import('naive-ui'))['NH6']
    NHighlight: (typeof import('naive-ui'))['NHighlight']
    NHr: (typeof import('naive-ui'))['NHr']
    NIcon: (typeof import('naive-ui'))['NIcon']
    NIconWrapper: (typeof import('naive-ui'))['NIconWrapper']
    NImage: (typeof import('naive-ui'))['NImage']
    NImageGroup: (typeof import('naive-ui'))['NImageGroup']
    NInfiniteScroll: (typeof import('naive-ui'))['NInfiniteScroll']
    NInput: (typeof import('naive-ui'))['NInput']
    NInputGroup: (typeof import('naive-ui'))['NInputGroup']
    NInputGroupLabel: (typeof import('naive-ui'))['NInputGroupLabel']
    NInputNumber: (typeof import('naive-ui'))['NInputNumber']
    NInputOtp: (typeof import('naive-ui'))['NInputOtp']
    NLayout: (typeof import('naive-ui'))['NLayout']
    NLayoutContent: (typeof import('naive-ui'))['NLayoutContent']
    NLayoutFooter: (typeof import('naive-ui'))['NLayoutFooter']
    NLayoutHeader: (typeof import('naive-ui'))['NLayoutHeader']
    NLayoutSider: (typeof import('naive-ui'))['NLayoutSider']
    NLegacyTransfer: (typeof import('naive-ui'))['NLegacyTransfer']
    NLi: (typeof import('naive-ui'))['NLi']
    NList: (typeof import('naive-ui'))['NList']
    NListItem: (typeof import('naive-ui'))['NListItem']
    NLoadingBarProvider: (typeof import('naive-ui'))['NLoadingBarProvider']
    NLog: (typeof import('naive-ui'))['NLog']
    NMarquee: (typeof import('naive-ui'))['NMarquee']
    NMention: (typeof import('naive-ui'))['NMention']
    NMenu: (typeof import('naive-ui'))['NMenu']
    NMessageProvider: (typeof import('naive-ui'))['NMessageProvider']
    NModal: (typeof import('naive-ui'))['NModal']
    NModalProvider: (typeof import('naive-ui'))['NModalProvider']
    NNotificationProvider: (typeof import('naive-ui'))['NNotificationProvider']
    NNumberAnimation: (typeof import('naive-ui'))['NNumberAnimation']
    NOl: (typeof import('naive-ui'))['NOl']
    NP: (typeof import('naive-ui'))['NP']
    NPageHeader: (typeof import('naive-ui'))['NPageHeader']
    NPagination: (typeof import('naive-ui'))['NPagination']
    NPerformantEllipsis: (typeof import('naive-ui'))['NPerformantEllipsis']
    NPopconfirm: (typeof import('naive-ui'))['NPopconfirm']
    NPopover: (typeof import('naive-ui'))['NPopover']
    NPopselect: (typeof import('naive-ui'))['NPopselect']
    NProgress: (typeof import('naive-ui'))['NProgress']
    NQrCode: (typeof import('naive-ui'))['NQrCode']
    NRadio: (typeof import('naive-ui'))['NRadio']
    NRadioButton: (typeof import('naive-ui'))['NRadioButton']
    NRadioGroup: (typeof import('naive-ui'))['NRadioGroup']
    NRate: (typeof import('naive-ui'))['NRate']
    NResult: (typeof import('naive-ui'))['NResult']
    NRow: (typeof import('naive-ui'))['NRow']
    NScrollbar: (typeof import('naive-ui'))['NScrollbar']
    NSelect: (typeof import('naive-ui'))['NSelect']
    NSkeleton: (typeof import('naive-ui'))['NSkeleton']
    NSlider: (typeof import('naive-ui'))['NSlider']
    NSpace: (typeof import('naive-ui'))['NSpace']
    NSpin: (typeof import('naive-ui'))['NSpin']
    NSplit: (typeof import('naive-ui'))['NSplit']
    NStatistic: (typeof import('naive-ui'))['NStatistic']
    NStep: (typeof import('naive-ui'))['NStep']
    NSteps: (typeof import('naive-ui'))['NSteps']
    NSwitch: (typeof import('naive-ui'))['NSwitch']
    NTab: (typeof import('naive-ui'))['NTab']
    NTabPane: (typeof import('naive-ui'))['NTabPane']
    NTable: (typeof import('naive-ui'))['NTable']
    NTabs: (typeof import('naive-ui'))['NTabs']
    NTag: (typeof import('naive-ui'))['NTag']
    NTbody: (typeof import('naive-ui'))['NTbody']
    NTd: (typeof import('naive-ui'))['NTd']
    NText: (typeof import('naive-ui'))['NText']
    NTh: (typeof import('naive-ui'))['NTh']
    NThead: (typeof import('naive-ui'))['NThead']
    NThing: (typeof import('naive-ui'))['NThing']
    NTime: (typeof import('naive-ui'))['NTime']
    NTimePicker: (typeof import('naive-ui'))['NTimePicker']
    NTimeline: (typeof import('naive-ui'))['NTimeline']
    NTimelineItem: (typeof import('naive-ui'))['NTimelineItem']
    NTooltip: (typeof import('naive-ui'))['NTooltip']
    NTr: (typeof import('naive-ui'))['NTr']
    NTransfer: (typeof import('naive-ui'))['NTransfer']
    NTree: (typeof import('naive-ui'))['NTree']
    NTreeSelect: (typeof import('naive-ui'))['NTreeSelect']
    NUl: (typeof import('naive-ui'))['NUl']
    NUpload: (typeof import('naive-ui'))['NUpload']
    NUploadDragger: (typeof import('naive-ui'))['NUploadDragger']
    NUploadFileList: (typeof import('naive-ui'))['NUploadFileList']
    NUploadTrigger: (typeof import('naive-ui'))['NUploadTrigger']
    NVirtualList: (typeof import('naive-ui'))['NVirtualList']
    NWatermark: (typeof import('naive-ui'))['NWatermark']
  }
}
export {}
