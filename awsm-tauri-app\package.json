{"name": "终章", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build"}, "dependencies": {"@tauri-apps/api": "^2.7.0", "@tauri-apps/plugin-opener": "^2", "naive-ui": "^2.42.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@tauri-apps/cli": "^2", "@vitejs/plugin-vue": "^5.2.1", "typescript": "~5.6.2", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}}