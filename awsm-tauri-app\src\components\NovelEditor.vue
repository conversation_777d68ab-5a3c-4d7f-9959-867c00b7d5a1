<template>
  <div class="novel-editor">
    <n-card title="小说编辑器" class="editor-card">
      <template #header-extra>
        <n-space>
          <n-button size="small" type="success" @click="saveDocument">
            <template #icon>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17 3H5C3.89 3 3 3.9 3 5V19C3 20.1 3.89 21 5 21H19C20.1 21 21 20.1 21 19V7L17 3M19 19H5V5H16.17L19 7.83V19M12 12C13.66 12 15 13.34 15 15S13.66 18 12 18 9 16.66 9 15 10.34 12 12 12Z"/>
              </svg>
            </template>
            保存
          </n-button>
          <n-button size="small" type="info" @click="exportDocument">
            <template #icon>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
              </svg>
            </template>
            导出
          </n-button>
        </n-space>
      </template>
      
      <n-space vertical size="large">
        <!-- 文档信息 -->
        <n-space>
          <n-input 
            v-model:value="documentTitle" 
            placeholder="请输入章节标题..."
            style="width: 300px;"
          />
          <n-tag type="info">字数: {{ wordCount }}</n-tag>
          <n-tag type="success">已保存</n-tag>
        </n-space>
        
        <!-- 编辑器工具栏 -->
        <n-space>
          <n-button-group>
            <n-button size="small" @click="insertText('**粗体**')">
              <strong>B</strong>
            </n-button>
            <n-button size="small" @click="insertText('*斜体*')">
              <em>I</em>
            </n-button>
            <n-button size="small" @click="insertText('~~删除线~~')">
              <s>S</s>
            </n-button>
          </n-button-group>
          
          <n-button size="small" type="primary" @click="aiAssist">
            <template #icon>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"/>
              </svg>
            </template>
            AI 助手
          </n-button>
        </n-space>
        
        <!-- 文本编辑区域 -->
        <n-input
          v-model:value="content"
          type="textarea"
          placeholder="开始你的创作..."
          :rows="15"
          class="editor-textarea"
          @input="updateWordCount"
        />
        
        <!-- 状态栏 -->
        <n-space justify="space-between">
          <n-space>
            <span class="status-text">行: {{ lineCount }}</span>
            <span class="status-text">列: {{ columnCount }}</span>
          </n-space>
          <n-space>
            <n-button size="small" quaternary @click="showSettings">设置</n-button>
            <n-button size="small" quaternary @click="showHelp">帮助</n-button>
          </n-space>
        </n-space>
      </n-space>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useMessage, useDialog } from 'naive-ui'

const message = useMessage()
const dialog = useDialog()

// 文档数据
const documentTitle = ref('第一章 开始')
const content = ref(`这是一个示例章节的开始。

在这里，你可以开始你的小说创作。这个编辑器集成了Naive UI组件，提供了现代化的用户界面。

你可以：
- 使用工具栏进行文本格式化
- 通过AI助手获得创作灵感
- 实时查看字数统计
- 保存和导出你的作品

开始你的创作之旅吧！`)

// 计算属性
const wordCount = computed(() => {
  return content.value.replace(/\s/g, '').length
})

const lineCount = computed(() => {
  return content.value.split('\n').length
})

const columnCount = computed(() => {
  const lines = content.value.split('\n')
  return lines[lines.length - 1].length + 1
})

// 方法
const updateWordCount = () => {
  // 实时更新字数统计
}

const insertText = (text: string) => {
  content.value += text
  message.info(`已插入: ${text}`)
}

const saveDocument = () => {
  // 这里可以调用Tauri命令保存到本地文件
  message.success('文档已保存')
}

const exportDocument = () => {
  // 这里可以实现导出功能
  dialog.info({
    title: '导出选项',
    content: '选择导出格式：TXT、DOCX、PDF',
    positiveText: '确定'
  })
}

const aiAssist = () => {
  // 这里可以集成AI助手功能
  dialog.info({
    title: 'AI 创作助手',
    content: '这里将集成AI模型来帮助你的创作。可以提供情节建议、角色发展、对话优化等功能。',
    positiveText: '了解'
  })
}

const showSettings = () => {
  message.info('设置功能开发中...')
}

const showHelp = () => {
  message.info('帮助文档开发中...')
}
</script>

<style scoped>
.novel-editor {
  padding: 20px;
  height: 100%;
}

.editor-card {
  height: 100%;
  background: rgba(26, 26, 26, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.editor-textarea {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
}

.status-text {
  color: #C0C0C0;
  font-size: 12px;
}

:deep(.n-input__textarea-el) {
  background: rgba(0, 0, 0, 0.3) !important;
  color: #FFFFFF !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

:deep(.n-input__textarea-el:focus) {
  border-color: rgba(255, 255, 255, 0.4) !important;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1) !important;
}
</style>
