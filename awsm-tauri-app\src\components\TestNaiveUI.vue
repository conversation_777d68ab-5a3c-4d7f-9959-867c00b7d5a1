<template>
  <div class="test-container">
    <n-card title="Naive UI 测试组件" class="test-card">
      <n-space vertical>
        <n-space>
          <n-button type="primary" @click="showMessage">显示消息</n-button>
          <n-button type="info" @click="showNotification">显示通知</n-button>
          <n-button type="success" @click="showDialog">显示对话框</n-button>
        </n-space>
        
        <n-input 
          v-model:value="inputValue" 
          placeholder="输入一些文本..." 
          class="test-input"
        />
        
        <n-space>
          <span>输入的内容：</span>
          <span class="input-display">{{ inputValue || '暂无内容' }}</span>
        </n-space>
      </n-space>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useMessage, useNotification, useDialog } from 'naive-ui'

const message = useMessage()
const notification = useNotification()
const dialog = useDialog()

const inputValue = ref('')

const showMessage = () => {
  message.success('这是一个成功消息！')
}

const showNotification = () => {
  notification.info({
    title: '通知标题',
    content: '这是一个信息通知，用于测试Naive UI的通知功能。',
    duration: 3000
  })
}

const showDialog = () => {
  dialog.info({
    title: '对话框测试',
    content: '这是一个信息对话框，用于测试Naive UI的对话框功能。主题已经配置为与你的应用风格一致。',
    positiveText: '确定',
    onPositiveClick: () => {
      message.success('你点击了确定按钮')
    }
  })
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 500px;
}

.test-card {
  background: rgba(26, 26, 26, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.test-input {
  width: 100%;
}

.input-display {
  color: #00FF55;
  font-weight: 500;
}
</style>
