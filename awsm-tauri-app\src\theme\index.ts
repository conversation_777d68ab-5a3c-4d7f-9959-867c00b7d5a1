import { darkTheme } from 'naive-ui'
import type { GlobalThemeOverrides } from 'naive-ui'

// 自定义深色主题，与你现有的设计保持一致
export const customDarkTheme: GlobalThemeOverrides = {
  common: {
    primaryColor: '#FFFFFF',
    primaryColorHover: '#F0F0F0',
    primaryColorPressed: '#E0E0E0',
    primaryColorSuppl: '#FFFFFF',
    
    infoColor: '#FFFFFF',
    infoColorHover: '#F0F0F0',
    infoColorPressed: '#E0E0E0',
    
    successColor: '#00FF55',
    successColorHover: '#33FF77',
    successColorPressed: '#00CC44',
    
    warningColor: '#FFCC00',
    warningColorHover: '#FFD633',
    warningColorPressed: '#CC9900',
    
    errorColor: '#D83F3F',
    errorColorHover: '#E55555',
    errorColorPressed: '#B33333',
    
    textColorBase: '#FFFFFF',
    textColor1: '#FFFFFF',
    textColor2: '#E0E0E0',
    textColor3: '#C0C0C0',
    textColorDisabled: '#808080',
    
    placeholderColor: '#808080',
    placeholderColorDisabled: '#606060',
    
    iconColor: '#FFFFFF',
    iconColorHover: '#F0F0F0',
    iconColorPressed: '#E0E0E0',
    iconColorDisabled: '#808080',
    
    opacity1: '0.82',
    opacity2: '0.72',
    opacity3: '0.38',
    opacity4: '0.24',
    opacity5: '0.18',
    
    dividerColor: 'rgba(255, 255, 255, 0.1)',
    borderColor: 'rgba(255, 255, 255, 0.2)',
    
    // 背景色配置，与你的黑色主题匹配
    bodyColor: '#000000',
    popoverColor: '#1A1A1A',
    cardColor: '#1A1A1A',
    modalColor: '#1A1A1A',
    tableColor: '#1A1A1A',
    
    tableHeaderColor: '#2A2A2A',
    inputColor: 'rgba(255, 255, 255, 0.1)',
    inputColorDisabled: 'rgba(255, 255, 255, 0.05)',
    
    buttonColor2: 'rgba(255, 255, 255, 0.08)',
    buttonColor2Hover: 'rgba(255, 255, 255, 0.12)',
    buttonColor2Pressed: 'rgba(255, 255, 255, 0.06)',
    
    boxShadow1: '0 1px 2px -2px rgba(0, 0, 0, .8), 0 3px 6px 0 rgba(0, 0, 0, .6), 0 5px 12px 4px rgba(0, 0, 0, .4)',
    boxShadow2: '0 3px 6px -4px rgba(0, 0, 0, .8), 0 6px 16px 0 rgba(0, 0, 0, .6), 0 9px 28px 8px rgba(0, 0, 0, .4)',
    boxShadow3: '0 6px 16px -9px rgba(0, 0, 0, .8), 0 9px 28px 0 rgba(0, 0, 0, .6), 0 12px 48px 16px rgba(0, 0, 0, .4)'
  },
  Button: {
    textColor: '#FFFFFF',
    textColorHover: '#FFFFFF',
    textColorPressed: '#FFFFFF',
    textColorFocus: '#FFFFFF',
    textColorDisabled: '#808080',
    color: 'rgba(255, 255, 255, 0.1)',
    colorHover: 'rgba(255, 255, 255, 0.15)',
    colorPressed: 'rgba(255, 255, 255, 0.08)',
    colorFocus: 'rgba(255, 255, 255, 0.12)',
    colorDisabled: 'rgba(255, 255, 255, 0.05)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    borderHover: '1px solid rgba(255, 255, 255, 0.3)',
    borderPressed: '1px solid rgba(255, 255, 255, 0.15)',
    borderFocus: '1px solid rgba(255, 255, 255, 0.25)',
    borderDisabled: '1px solid rgba(255, 255, 255, 0.1)'
  },
  Input: {
    color: 'rgba(255, 255, 255, 0.1)',
    colorFocus: 'rgba(255, 255, 255, 0.15)',
    colorDisabled: 'rgba(255, 255, 255, 0.05)',
    textColor: '#FFFFFF',
    textColorDisabled: '#808080',
    placeholderColor: '#808080',
    placeholderColorDisabled: '#606060',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    borderHover: '1px solid rgba(255, 255, 255, 0.3)',
    borderFocus: '1px solid rgba(255, 255, 255, 0.4)',
    borderDisabled: '1px solid rgba(255, 255, 255, 0.1)',
    boxShadowFocus: '0 0 0 2px rgba(255, 255, 255, 0.2)'
  },
  Card: {
    color: '#1A1A1A',
    colorModal: '#1A1A1A',
    colorPopover: '#1A1A1A',
    colorEmbedded: '#0A0A0A',
    textColor: '#FFFFFF',
    titleTextColor: '#FFFFFF',
    borderColor: 'rgba(255, 255, 255, 0.1)',
    actionColor: 'rgba(255, 255, 255, 0.05)'
  }
}

// 导出配置好的主题
export { darkTheme }
